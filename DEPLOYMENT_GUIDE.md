# 部署指南

本文档提供遥感图像分类系统的简化部署说明，适合快速上手。

## 🚀 快速部署

### 方式一：Docker 部署（推荐）

#### 1. 准备环境
```bash
# 确保已安装 Docker
docker --version

# 克隆项目
git clone <repository-url>
cd yaogan-zsy
```

#### 2. 构建并运行
```bash
# 构建生产镜像
docker build --target production -t rsic:latest .

# 运行容器
docker run -d \
  --name rsic-app \
  -p 8000:8000 \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/outputs:/app/outputs \
  rsic:latest

# 检查运行状态
docker ps
docker logs rsic-app
```

#### 3. 访问应用
- Web界面: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/api/health

### 方式二：本地部署

#### 1. 环境配置
```bash
# 创建虚拟环境
conda create -n remote_sensing python=3.10
conda activate remote_sensing

# 安装依赖
pip install -r requirements.txt
```

#### 2. 启动应用
```bash
# 启动 Web 应用
python src/web_app.py

# 或启动 Gradio 界面
python src/app.py
```

## 🔧 生产环境部署

### 环境变量配置
```bash
# 创建环境文件
cat > .env << EOF
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
WORKERS=4
EOF
```

### 使用环境文件运行
```bash
docker run -d \
  --name rsic-prod \
  -p 8000:8000 \
  --env-file .env \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/new_dataset:/app/new_dataset \
  rsic:latest
```

### 数据持久化
```bash
# 创建数据卷
docker volume create rsic-data
docker volume create rsic-outputs

# 使用数据卷运行
docker run -d \
  --name rsic-persistent \
  -p 8000:8000 \
  -v rsic-data:/app/data \
  -v rsic-outputs:/app/outputs \
  -v $(pwd)/old_dataset:/app/old_dataset \
  rsic:latest
```

## 🖥️ GPU 支持

### NVIDIA Docker 配置
```bash
# 安装 NVIDIA Docker
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker

# 运行 GPU 容器
docker run --gpus all -d \
  --name rsic-gpu \
  -p 8000:8000 \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/outputs:/app/outputs \
  rsic:latest
```

## 🔍 监控和维护

### 容器管理
```bash
# 查看运行状态
docker ps -a

# 查看日志
docker logs -f rsic-app

# 进入容器调试
docker exec -it rsic-app bash

# 重启容器
docker restart rsic-app

# 停止和删除
docker stop rsic-app
docker rm rsic-app
```

### 健康检查
```bash
# 检查应用健康状态
curl http://localhost:8000/api/health

# 检查容器健康状态
docker inspect rsic-app | grep Health -A 10
```

### 日志管理
```bash
# 限制日志大小
docker run -d \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  --name rsic-app \
  -p 8000:8000 \
  rsic:latest

# 查看实时日志
docker logs -f --tail 100 rsic-app
```

## 🛠️ 故障排除

### 常见问题

#### 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep 8000

# 使用不同端口
docker run -p 8001:8000 rsic:latest
```

#### 内存不足
```bash
# 限制内存使用
docker run -m 2g rsic:latest

# 使用 CPU 版本
docker build -f Dockerfile.cpu -t rsic:cpu .
docker run rsic:cpu
```

#### 权限问题
```bash
# 修复文件权限
sudo chown -R 1000:1000 outputs/
sudo chown -R 1000:1000 old_dataset/
```

### 性能优化
```bash
# 增加 worker 数量
docker run -e WORKERS=8 rsic:latest

# 优化内存设置
docker run -e OMP_NUM_THREADS=4 -e MKL_NUM_THREADS=4 rsic:latest
```

## 📋 部署检查清单

- [ ] Docker 已安装并运行
- [ ] 项目代码已下载
- [ ] 数据集已准备（old_dataset 目录）
- [ ] 端口 8000 可用
- [ ] 有足够的磁盘空间（至少 10GB）
- [ ] GPU 驱动已安装（如需要）
- [ ] 容器成功启动
- [ ] Web 界面可访问
- [ ] API 健康检查通过

## 📚 更多信息

- 详细 Docker 配置: [DOCKER_GUIDE.md](DOCKER_GUIDE.md)
- 前端使用说明: [static/README.md](static/README.md)
- 项目完整文档: [README.md](README.md)

---

**提示**: 如遇到问题，请查看容器日志或参考详细的故障排除文档。
