"""
模型管理API测试脚本

测试模型管理API的功能
"""

import sys
sys.path.append('src')

from fastapi.testclient import TestClient
from api.main import app

# 创建测试客户端
client = TestClient(app)

def test_get_model_types():
    """测试获取模型类型列表"""
    response = client.get("/api/models/types")
    print(f"模型类型状态码: {response.status_code}")
    data = response.json()
    print(f"模型类型响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and "model_types" in data and data["success"]
    return False

def test_get_models_summary():
    """测试获取模型摘要"""
    response = client.get("/api/models/summary")
    print(f"模型摘要状态码: {response.status_code}")
    data = response.json()
    print(f"模型摘要响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and "models_summary" in data and data["success"]
    return False

def test_get_model_info():
    """测试获取模型详细信息"""
    response = client.get("/api/models/resnet50/info")
    print(f"模型信息状态码: {response.status_code}")
    data = response.json()
    print(f"模型信息响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and "model_info" in data and data["success"]
    return False

def test_get_pruning_comparison():
    """测试获取剪枝比较数据"""
    response = client.get("/api/models/resnet50/comparison/pruning")
    print(f"剪枝比较状态码: {response.status_code}")
    data = response.json()
    print(f"剪枝比较响应: {data}")
    
    # 即使没有数据，也应该返回200状态码
    return response.status_code == 200

def test_get_distillation_comparison():
    """测试获取蒸馏比较数据"""
    response = client.get("/api/models/resnet50/comparison/distillation")
    print(f"蒸馏比较状态码: {response.status_code}")
    data = response.json()
    print(f"蒸馏比较响应: {data}")
    
    return response.status_code == 200

def test_get_quantization_comparison():
    """测试获取量化比较数据"""
    response = client.get("/api/models/resnet50/comparison/quantization")
    print(f"量化比较状态码: {response.status_code}")
    data = response.json()
    print(f"量化比较响应: {data}")
    
    return response.status_code == 200

def test_get_all_comparisons():
    """测试获取所有比较数据"""
    response = client.get("/api/models/resnet50/comparison")
    print(f"所有比较状态码: {response.status_code}")
    data = response.json()
    print(f"所有比较响应: {data}")
    
    return response.status_code == 200

if __name__ == "__main__":
    print("开始测试模型管理API...")
    
    tests = [
        ("模型类型列表", test_get_model_types),
        ("模型摘要", test_get_models_summary),
        ("模型详细信息", test_get_model_info),
        ("剪枝比较", test_get_pruning_comparison),
        ("蒸馏比较", test_get_distillation_comparison),
        ("量化比较", test_get_quantization_comparison),
        ("所有比较", test_get_all_comparisons),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n=== 测试 {test_name} ===")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
