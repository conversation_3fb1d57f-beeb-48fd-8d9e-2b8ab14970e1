# 遥感图像分类系统 - 前端界面

这是遥感图像分类系统的现代化Web前端界面，使用HTML、CSS和JavaScript构建，提供直观的用户体验。

## 功能特性

### 🖼️ 图像预测
- 支持拖拽上传图像文件
- 多种模型选择（ResNet50、DenseNet201、ViT、Swin Transformer等）
- 实时预测结果显示
- 推理时间统计

### 📊 模型对比
- 剪枝模型性能对比
- 知识蒸馏效果对比  
- 模型量化结果对比
- 详细的性能指标表格

### 🗂️ 数据集可视化
- 训练数据集样本展示
- 新数据集样本展示
- 数据集对比分析

### ⚙️ 自适应微调
- 自动监控数据分布变化
- 手动触发模型微调
- 实时状态监控
- 可调节的阈值设置

## 技术架构

### 前端技术栈
- **HTML5**: 语义化标记，提供良好的结构
- **CSS3**: 扁平化设计，响应式布局
- **JavaScript (ES6+)**: 现代化交互逻辑
- **Font Awesome**: 图标库

### 设计特点
- **扁平化设计**: 简洁现代的视觉风格
- **响应式布局**: 适配不同屏幕尺寸
- **直观交互**: 拖拽上传、实时反馈
- **模块化结构**: 清晰的代码组织

## 文件结构

```
static/
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   └── main.js        # 主要JavaScript逻辑
└── README.md          # 说明文档
```

## 使用方法

### 启动Web应用
```bash
# 进入项目根目录
cd /path/to/yaogan-zsy

# 启动FastAPI服务器
python src/web_app.py
```

### 访问界面
- 主页面: http://localhost:8000
- API文档: http://localhost:8000/docs

## 界面说明

### 1. 图像预测页面
- **上传区域**: 支持拖拽或点击上传图像
- **模型选择**: 下拉菜单选择预测模型
- **预测按钮**: 开始图像分类预测
- **结果显示**: 显示Top-5预测结果和置信度

### 2. 模型对比页面
- **对比类型**: 选择剪枝、蒸馏或量化对比
- **模型类型**: 选择要对比的模型架构
- **对比表格**: 详细的性能指标对比

### 3. 数据集页面
- **样本展示**: 显示训练和新数据集的样本图像
- **刷新功能**: 重新加载数据集样本
- **分类信息**: 显示图像的类别标签

### 4. 自适应微调页面
- **阈值设置**: 调节分布差异检测阈值
- **监控控制**: 启动/停止自动监控
- **手动微调**: 立即触发模型微调
- **状态显示**: 实时显示系统状态

## API接口

### 核心接口
- `GET /`: 主页面
- `GET /api/models`: 获取可用模型列表
- `POST /api/predict`: 图像预测
- `GET /api/comparison/{type}/{model}`: 获取模型对比数据
- `GET /api/dataset/samples`: 获取数据集样本

### 自适应微调接口
- `POST /api/adaptive/start`: 启动监控
- `POST /api/adaptive/stop`: 停止监控
- `POST /api/adaptive/manual-finetune`: 手动微调
- `GET /api/adaptive/status`: 获取状态
- `GET /api/adaptive/check-distribution`: 检查分布

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发说明

### 自定义样式
编辑 `css/style.css` 文件来修改界面样式。主要的CSS变量：
- 主色调: `#3498db`
- 成功色: `#27ae60`
- 警告色: `#f39c12`
- 错误色: `#e74c3c`

### 添加功能
在 `js/main.js` 中添加新的JavaScript功能。主要的函数：
- `predictImage()`: 图像预测
- `loadComparisonTable()`: 加载对比表格
- `startAdaptiveMonitoring()`: 启动监控
- `showToast()`: 显示通知

### 响应式设计
界面使用CSS Grid和Flexbox布局，在768px以下自动切换为移动端布局。

## 注意事项

1. **文件上传**: 仅支持JPG、PNG、JPEG格式的图像文件
2. **模型加载**: 首次使用模型时需要加载时间
3. **网络连接**: 需要稳定的网络连接以获取预测结果
4. **浏览器缓存**: 更新后可能需要清除浏览器缓存

## 故障排除

### 常见问题
1. **页面无法加载**: 检查FastAPI服务是否正常启动
2. **预测失败**: 确认模型文件存在且格式正确
3. **图像上传失败**: 检查文件格式和大小限制
4. **样式异常**: 清除浏览器缓存并刷新页面

### 调试方法
1. 打开浏览器开发者工具查看控制台错误
2. 检查网络请求状态
3. 查看FastAPI服务器日志输出

## 更新日志

### v1.0.0 (2025-06-16)
- 初始版本发布
- 完整的图像预测功能
- 模型对比可视化
- 数据集样本展示
- 自适应微调控制面板
- 响应式设计支持
