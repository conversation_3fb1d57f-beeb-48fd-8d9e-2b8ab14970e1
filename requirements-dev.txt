# Development Dependencies
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0
pytest-benchmark>=4.0.0
httpx>=0.24.0  # For testing FastAPI endpoints

# Code Quality and Formatting
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
flake8-docstrings>=1.7.0
flake8-import-order>=0.18.0
bandit>=1.7.0
mypy>=1.5.0
pylint>=2.17.0

# Pre-commit Hooks
pre-commit>=3.3.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0
sphinx-autodoc-typehints>=1.24.0

# Jupyter and Notebooks
jupyter>=1.0.0
jupyterlab>=4.0.0
notebook>=7.0.0
ipywidgets>=8.1.0
nbconvert>=7.7.0

# Performance Profiling
line-profiler>=4.1.0
memory-profiler>=0.61.0
py-spy>=0.3.0
scalene>=1.5.0

# Database and Migration Tools (if needed)
alembic>=1.11.0
sqlalchemy>=2.0.0

# API Documentation
redoc>=2.1.0
swagger-ui-bundle>=0.0.9

# Development Utilities
ipython>=8.14.0
ipdb>=0.13.0
watchdog>=3.0.0
python-dotenv>=1.0.0

# Build and Packaging
build>=0.10.0
twine>=4.0.0
wheel>=0.41.0
setuptools>=68.0.0

# Security Scanning
safety>=2.3.0
pip-audit>=2.6.0

# Environment Management
pipenv>=2023.7.0
poetry>=1.5.0

# Git Hooks and Automation
gitpython>=3.1.0
pre-commit>=3.3.0

# Load Testing (for web endpoints)
locust>=2.16.0

# Mock and Fixtures
factory-boy>=3.3.0
faker>=19.3.0
responses>=0.23.0

# Debugging and Development
pdbpp>=0.10.0
better-exceptions>=0.3.0
icecream>=2.1.0

# Type Checking
types-requests>=2.31.0
types-Pillow>=10.0.0
types-setuptools>=68.0.0

# Documentation Building
mkdocs>=1.5.0
mkdocs-material>=9.1.0
mkdocs-mermaid2-plugin>=1.1.0

# Performance Monitoring
py-spy>=0.3.0
memray>=1.8.0

# Code Coverage
coverage[toml]>=7.2.0
codecov>=2.1.0

# Linting for specific file types
yamllint>=1.32.0
jsonschema>=4.18.0

# Development Server
watchfiles>=0.19.0
python-multipart>=0.0.6

# Database Development
sqlite-utils>=3.34.0
datasette>=0.64.0

# API Testing
tavern>=2.0.0
schemathesis>=3.19.0
