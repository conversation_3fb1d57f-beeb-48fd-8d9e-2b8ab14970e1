"""
自适应微调API测试脚本

测试自适应微调API的功能
"""

import sys
sys.path.append('src')

from fastapi.testclient import TestClient
from api.main import app

# 创建测试客户端
client = TestClient(app)

def test_get_adaptive_status():
    """测试获取自适应状态"""
    response = client.get("/api/adaptive/status")
    print(f"自适应状态状态码: {response.status_code}")
    data = response.json()
    print(f"自适应状态响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and data["success"]
    return False

def test_get_adaptive_config():
    """测试获取自适应配置"""
    response = client.get("/api/adaptive/config")
    print(f"自适应配置状态码: {response.status_code}")
    data = response.json()
    print(f"自适应配置响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and "config" in data and data["success"]
    return False

def test_check_distribution():
    """测试检查数据分布"""
    response = client.post("/api/adaptive/check-distribution", json={})
    print(f"分布检查状态码: {response.status_code}")
    data = response.json()
    print(f"分布检查响应: {data}")
    
    return response.status_code == 200

def test_update_threshold():
    """测试更新阈值"""
    response = client.put("/api/adaptive/threshold", json={"threshold": 0.15})
    print(f"更新阈值状态码: {response.status_code}")
    data = response.json()
    print(f"更新阈值响应: {data}")
    
    return response.status_code == 200

def test_start_monitoring():
    """测试启动监控"""
    response = client.post("/api/adaptive/start")
    print(f"启动监控状态码: {response.status_code}")
    data = response.json()
    print(f"启动监控响应: {data}")
    
    return response.status_code == 200

def test_stop_monitoring():
    """测试停止监控"""
    response = client.post("/api/adaptive/stop")
    print(f"停止监控状态码: {response.status_code}")
    data = response.json()
    print(f"停止监控响应: {data}")
    
    return response.status_code == 200

def test_manual_tune():
    """测试手动微调"""
    response = client.post("/api/adaptive/manual-tune", json={
        "model_type": "resnet50",
        "epochs": 3,
        "learning_rate": 1e-4
    })
    print(f"手动微调状态码: {response.status_code}")
    data = response.json()
    print(f"手动微调响应: {data}")
    
    return response.status_code == 200

def test_fine_tuning_status():
    """测试获取微调状态"""
    response = client.get("/api/adaptive/fine-tuning-status")
    print(f"微调状态状态码: {response.status_code}")
    data = response.json()
    print(f"微调状态响应: {data}")
    
    return response.status_code == 200

if __name__ == "__main__":
    print("开始测试自适应微调API...")
    
    tests = [
        ("自适应状态", test_get_adaptive_status),
        ("自适应配置", test_get_adaptive_config),
        ("分布检查", test_check_distribution),
        ("更新阈值", test_update_threshold),
        ("启动监控", test_start_monitoring),
        ("停止监控", test_stop_monitoring),
        ("手动微调", test_manual_tune),
        ("微调状态", test_fine_tuning_status),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n=== 测试 {test_name} ===")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
