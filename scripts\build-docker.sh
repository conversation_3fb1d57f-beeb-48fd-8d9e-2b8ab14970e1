#!/bin/bash

# =============================================================================
# Docker Build Script for Remote Sensing Image Classification System
# Builds different Docker images for various deployment scenarios
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="remote-sensing-classification"
TAG="latest"
BUILD_TYPE="production"
PUSH_TO_REGISTRY=false
REGISTRY=""
PYTHON_VERSION="3.10"
CUDA_VERSION="12.2.2"
UBUNTU_VERSION="22.04"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Build Docker images for Remote Sensing Image Classification System

OPTIONS:
    -t, --type TYPE         Build type: production, development, cpu, jupyter (default: production)
    -n, --name NAME         Image name (default: remote-sensing-classification)
    --tag TAG               Image tag (default: latest)
    -p, --push              Push to registry after build
    -r, --registry URL      Registry URL (required if --push is used)
    --python VERSION        Python version (default: 3.10)
    --cuda VERSION          CUDA version (default: 12.2.2)
    --ubuntu VERSION        Ubuntu version (default: 22.04)
    --no-cache              Build without using cache
    -h, --help              Show this help message

EXAMPLES:
    # Build production image
    $0 --type production

    # Build development image
    $0 --type development --tag dev

    # Build CPU-only image
    $0 --type cpu --tag cpu-latest

    # Build Jupyter image
    $0 --type jupyter --tag jupyter-latest

    # Build and push to registry
    $0 --type production --push --registry your-registry.com

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to build image
build_image() {
    local dockerfile="Dockerfile"
    local build_args=""
    local full_image_name="${IMAGE_NAME}:${TAG}"
    
    # Add registry prefix if specified
    if [[ -n "$REGISTRY" ]]; then
        full_image_name="${REGISTRY}/${full_image_name}"
    fi
    
    # Set dockerfile based on build type
    case $BUILD_TYPE in
        "production")
            dockerfile="Dockerfile"
            build_args="--target production"
            ;;
        "development")
            dockerfile="Dockerfile"
            build_args="--target development"
            ;;
        "cpu")
            dockerfile="Dockerfile.cpu"
            build_args="--target production"
            ;;
        "jupyter")
            dockerfile="Dockerfile.jupyter"
            build_args=""
            ;;
        *)
            print_error "Unknown build type: $BUILD_TYPE"
            exit 1
            ;;
    esac
    
    # Add build arguments
    build_args="$build_args --build-arg PYTHON_VERSION=$PYTHON_VERSION"
    build_args="$build_args --build-arg CUDA_VERSION=$CUDA_VERSION"
    build_args="$build_args --build-arg UBUNTU_VERSION=$UBUNTU_VERSION"
    
    # Add no-cache if specified
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    print_info "Building Docker image..."
    print_info "Image name: $full_image_name"
    print_info "Dockerfile: $dockerfile"
    print_info "Build type: $BUILD_TYPE"
    print_info "Python version: $PYTHON_VERSION"
    
    if [[ "$BUILD_TYPE" != "cpu" ]]; then
        print_info "CUDA version: $CUDA_VERSION"
    fi
    
    # Build the image
    docker build \
        -f "$dockerfile" \
        -t "$full_image_name" \
        $build_args \
        .
    
    if [[ $? -eq 0 ]]; then
        print_success "Docker image built successfully: $full_image_name"
    else
        print_error "Failed to build Docker image"
        exit 1
    fi
    
    # Show image size
    local image_size=$(docker images --format "table {{.Size}}" "$full_image_name" | tail -n 1)
    print_info "Image size: $image_size"
}

# Function to push image to registry
push_image() {
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        if [[ -z "$REGISTRY" ]]; then
            print_error "Registry URL is required when pushing"
            exit 1
        fi
        
        local full_image_name="${REGISTRY}/${IMAGE_NAME}:${TAG}"
        
        print_info "Pushing image to registry: $full_image_name"
        docker push "$full_image_name"
        
        if [[ $? -eq 0 ]]; then
            print_success "Image pushed successfully"
        else
            print_error "Failed to push image"
            exit 1
        fi
    fi
}

# Function to run basic tests on the built image
test_image() {
    local full_image_name="${IMAGE_NAME}:${TAG}"
    
    if [[ -n "$REGISTRY" ]]; then
        full_image_name="${REGISTRY}/${full_image_name}"
    fi
    
    print_info "Running basic tests on the image..."
    
    # Test if the image can start
    case $BUILD_TYPE in
        "jupyter")
            print_info "Testing Jupyter image startup..."
            docker run --rm -d --name test-container -p 8889:8888 "$full_image_name" &
            sleep 10
            if docker ps | grep -q test-container; then
                print_success "Jupyter container started successfully"
                docker stop test-container
            else
                print_error "Jupyter container failed to start"
                exit 1
            fi
            ;;
        *)
            print_info "Testing application startup..."
            docker run --rm --name test-container "$full_image_name" python -c "import src; print('Import successful')" &
            sleep 5
            if [[ $? -eq 0 ]]; then
                print_success "Application imports working correctly"
            else
                print_error "Application import test failed"
                exit 1
            fi
            ;;
    esac
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --python)
            PYTHON_VERSION="$2"
            shift 2
            ;;
        --cuda)
            CUDA_VERSION="$2"
            shift 2
            ;;
        --ubuntu)
            UBUNTU_VERSION="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "Starting Docker build process..."
    print_info "Build type: $BUILD_TYPE"
    print_info "Image name: $IMAGE_NAME"
    print_info "Tag: $TAG"
    
    check_prerequisites
    build_image
    test_image
    push_image
    
    print_success "Docker build process completed successfully!"
    
    # Show final image information
    local full_image_name="${IMAGE_NAME}:${TAG}"
    if [[ -n "$REGISTRY" ]]; then
        full_image_name="${REGISTRY}/${full_image_name}"
    fi
    
    print_info "Built image: $full_image_name"
    print_info "To run the image:"
    
    case $BUILD_TYPE in
        "jupyter")
            echo "  docker run -p 8888:8888 $full_image_name"
            ;;
        *)
            echo "  docker run -p 8000:8000 $full_image_name"
            ;;
    esac
}

# Run main function
main
