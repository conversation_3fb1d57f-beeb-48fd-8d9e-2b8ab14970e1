# =============================================================================
# Jupyter Development Dockerfile
# Optimized for data science and model development workflows
# =============================================================================

ARG PYTHON_VERSION=3.10
ARG CUDA_VERSION=12.2.2
ARG UBUNTU_VERSION=22.04

FROM nvidia/cuda:${CUDA_VERSION}-base-ubuntu${UBUNTU_VERSION}

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    DEBIAN_FRONTEND=noninteractive \
    JUPYTER_ENABLE_LAB=yes \
    JUPYTER_TOKEN=your-secure-token \
    JUPYTER_PORT=8888

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python3-pip \
    build-essential \
    curl \
    wget \
    git \
    vim \
    htop \
    tree \
    ca-certificates \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create symbolic links for python
RUN ln -sf /usr/bin/python${PYTHON_VERSION} /usr/bin/python3 \
    && ln -sf /usr/bin/python3 /usr/bin/python

# Upgrade pip
RUN python -m pip install --upgrade pip setuptools wheel

# Set working directory
WORKDIR /app

# Copy dependency files
COPY requirements.txt requirements-dev.txt pyproject.toml setup.py ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir -r requirements-dev.txt \
    && pip install --no-cache-dir -e .

# Install additional Jupyter and data science tools
RUN pip install --no-cache-dir \
    jupyter \
    jupyterlab \
    notebook \
    ipywidgets \
    jupyter-contrib-nbextensions \
    jupyter-nbextensions-configurator \
    jupyterlab-git \
    jupyterlab-lsp \
    python-lsp-server[all] \
    voila \
    papermill \
    nbconvert \
    nbformat \
    nbstripout

# Install JupyterLab extensions
RUN jupyter labextension install --no-build \
    @jupyter-widgets/jupyterlab-manager \
    @jupyterlab/git \
    @jupyterlab/toc \
    && jupyter lab build --dev-build=False --minimize=False

# Enable Jupyter extensions
RUN jupyter contrib nbextension install --system \
    && jupyter nbextensions_configurator enable --system

# Create jupyter user
RUN useradd -m -s /bin/bash jupyter \
    && chown -R jupyter:jupyter /app

# Copy source code
COPY --chown=jupyter:jupyter . /app/

# Create necessary directories
RUN mkdir -p /app/notebooks /app/data /app/outputs /app/logs \
    && chown -R jupyter:jupyter /app

# Switch to jupyter user
USER jupyter

# Create Jupyter configuration
RUN jupyter lab --generate-config \
    && echo "c.ServerApp.ip = '0.0.0.0'" >> ~/.jupyter/jupyter_lab_config.py \
    && echo "c.ServerApp.port = 8888" >> ~/.jupyter/jupyter_lab_config.py \
    && echo "c.ServerApp.open_browser = False" >> ~/.jupyter/jupyter_lab_config.py \
    && echo "c.ServerApp.allow_root = True" >> ~/.jupyter/jupyter_lab_config.py \
    && echo "c.ServerApp.token = '${JUPYTER_TOKEN}'" >> ~/.jupyter/jupyter_lab_config.py \
    && echo "c.ServerApp.notebook_dir = '/app'" >> ~/.jupyter/jupyter_lab_config.py

# Expose Jupyter port
EXPOSE 8888

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8888/lab || exit 1

# Start Jupyter Lab
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--port=8888", "--no-browser", "--allow-root"]
