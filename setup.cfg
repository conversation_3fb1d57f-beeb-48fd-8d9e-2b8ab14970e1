[metadata]
name = remote-sensing-classification
version = 0.1.0
author = Remote Sensing Team
author_email = <EMAIL>
description = Remote Sensing Image Classification System with Model Optimization
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/your-org/remote-sensing-classification
project_urls =
    Bug Tracker = https://github.com/your-org/remote-sensing-classification/issues
    Documentation = https://github.com/your-org/remote-sensing-classification/docs
    Source Code = https://github.com/your-org/remote-sensing-classification
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: Science/Research
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Scientific/Engineering :: Artificial Intelligence
    Topic :: Scientific/Engineering :: Image Recognition

[options]
package_dir =
    = src
packages = find:
python_requires = >=3.8
install_requires =
    torch>=2.0.0
    torchvision>=0.15.0
    numpy>=1.24.0
    pandas>=2.0.0
    scikit-learn>=1.3.0
    matplotlib>=3.7.0
    Pillow>=10.0.0
    tqdm>=4.65.0
    fastapi>=0.100.0
    uvicorn[standard]>=0.23.0
    python-multipart>=0.0.6
    gradio>=3.40.0
    timm>=0.9.0
    pydantic>=2.0.0
    python-dotenv>=1.0.0
    loguru>=0.7.0
    rich>=13.0.0
    click>=8.1.0

[options.packages.find]
where = src

[options.extras_require]
web =
    fastapi>=0.100.0
    uvicorn[standard]>=0.23.0
    python-multipart>=0.0.6
    jinja2>=3.1.0
    aiofiles>=23.0.0
    websockets>=11.0.0
ui =
    gradio>=3.40.0
    streamlit>=1.25.0
    plotly>=5.15.0
monitoring =
    wandb>=0.15.0
    mlflow>=2.5.0
    tensorboard>=2.13.0
optimization =
    pytorch-lightning>=2.0.0
    torchmetrics>=1.0.0
    albumentations>=1.3.0
dev =
    pytest>=7.4.0
    pytest-asyncio>=0.21.0
    pytest-cov>=4.1.0
    black>=23.7.0
    isort>=5.12.0
    flake8>=6.0.0
    mypy>=1.5.0
    pre-commit>=3.3.0
    sphinx>=7.1.0
    jupyter>=1.0.0
all =
    remote-sensing-classification[web,ui,monitoring,optimization]

[options.entry_points]
console_scripts =
    rsic-train = src.train:main
    rsic-evaluate = src.evaluate:main
    rsic-predict = src.app:main
    rsic-web = src.web_app:main
    rsic-demo = src.demo_web_app:main

[flake8]
max-line-length = 88
extend-ignore = E203, E266, E501, W503, F403, F401
max-complexity = 18
select = B,C,E,F,W,T4,B9
exclude =
    .git,
    __pycache__,
    .tox,
    .eggs,
    *.egg,
    build,
    dist,
    .venv,
    venv,
    .env

[mypy]
python_version = 3.8
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

[mypy-torch.*]
ignore_missing_imports = True

[mypy-torchvision.*]
ignore_missing_imports = True

[mypy-sklearn.*]
ignore_missing_imports = True

[mypy-matplotlib.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-gradio.*]
ignore_missing_imports = True

[mypy-timm.*]
ignore_missing_imports = True

[mypy-wandb.*]
ignore_missing_imports = True

[mypy-mlflow.*]
ignore_missing_imports = True

[tool:pytest]
minversion = 7.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    web: marks tests as web interface tests
    model: marks tests as model-related tests

[coverage:run]
source = src
omit =
    */tests/*
    */test_*
    setup.py
    */venv/*
    */.venv/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[isort]
profile = black
multi_line_output = 3
line_length = 88
known_first_party = src
known_third_party = torch,torchvision,numpy,pandas,sklearn,matplotlib,PIL,fastapi,gradio

[bandit]
exclude_dirs = tests,test_*
skips = B101,B601

[wheel]
universal = 0
