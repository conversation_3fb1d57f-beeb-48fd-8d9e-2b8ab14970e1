"""
数据集管理API测试脚本

测试数据集管理API的功能
"""

import sys
sys.path.append('src')

from fastapi.testclient import TestClient
from api.main import app

# 创建测试客户端
client = TestClient(app)

def test_get_dataset_stats():
    """测试获取数据集统计"""
    response = client.get("/api/dataset/stats")
    print(f"数据集统计状态码: {response.status_code}")
    data = response.json()
    print(f"数据集统计响应: {data}")
    
    if response.status_code == 200:
        return "success" in data and data["success"]
    return False

def test_get_old_dataset_info():
    """测试获取旧数据集信息"""
    response = client.get("/api/dataset/info/old")
    print(f"旧数据集信息状态码: {response.status_code}")
    data = response.json()
    print(f"旧数据集信息响应: {data}")
    
    return response.status_code == 200

def test_get_new_dataset_info():
    """测试获取新数据集信息"""
    response = client.get("/api/dataset/info/new")
    print(f"新数据集信息状态码: {response.status_code}")
    data = response.json()
    print(f"新数据集信息响应: {data}")
    
    return response.status_code == 200

def test_get_old_dataset_samples():
    """测试获取旧数据集示例"""
    response = client.get("/api/dataset/samples?dataset_type=old&num_classes=2&num_images_per_class=2")
    print(f"旧数据集示例状态码: {response.status_code}")
    data = response.json()
    print(f"旧数据集示例响应: {data}")
    
    return response.status_code == 200

def test_get_new_dataset_samples():
    """测试获取新数据集示例"""
    response = client.get("/api/dataset/samples?dataset_type=new&num_classes=2&num_images_per_class=2")
    print(f"新数据集示例状态码: {response.status_code}")
    data = response.json()
    print(f"新数据集示例响应: {data}")
    
    return response.status_code == 200

def test_get_dataset_comparison():
    """测试获取数据集对比"""
    response = client.get("/api/dataset/comparison?num_images_per_dataset=3")
    print(f"数据集对比状态码: {response.status_code}")
    data = response.json()
    print(f"数据集对比响应: {data}")
    
    return response.status_code == 200

def test_post_dataset_samples():
    """测试POST方式获取数据集示例"""
    response = client.post("/api/dataset/samples", json={
        "dataset_type": "old",
        "num_classes": 2,
        "num_images_per_class": 2,
        "include_base64": False
    })
    print(f"POST数据集示例状态码: {response.status_code}")
    data = response.json()
    print(f"POST数据集示例响应: {data}")
    
    return response.status_code == 200

def test_post_dataset_comparison():
    """测试POST方式获取数据集对比"""
    response = client.post("/api/dataset/comparison", json={
        "num_images_per_dataset": 3,
        "include_base64": False,
        "specific_class": None
    })
    print(f"POST数据集对比状态码: {response.status_code}")
    data = response.json()
    print(f"POST数据集对比响应: {data}")
    
    return response.status_code == 200

if __name__ == "__main__":
    print("开始测试数据集管理API...")
    
    tests = [
        ("数据集统计", test_get_dataset_stats),
        ("旧数据集信息", test_get_old_dataset_info),
        ("新数据集信息", test_get_new_dataset_info),
        ("旧数据集示例", test_get_old_dataset_samples),
        ("新数据集示例", test_get_new_dataset_samples),
        ("数据集对比", test_get_dataset_comparison),
        ("POST数据集示例", test_post_dataset_samples),
        ("POST数据集对比", test_post_dataset_comparison),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n=== 测试 {test_name} ===")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
