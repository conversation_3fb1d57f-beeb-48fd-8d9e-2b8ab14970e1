@echo off
REM =============================================================================
REM Docker Build Script for Remote Sensing Image Classification System (Windows)
REM Builds different Docker images for various deployment scenarios
REM =============================================================================

setlocal enabledelayedexpansion

REM Default values
set IMAGE_NAME=remote-sensing-classification
set TAG=latest
set BUILD_TYPE=production
set PUSH_TO_REGISTRY=false
set REGISTRY=
set PYTHON_VERSION=3.10
set CUDA_VERSION=12.2.2
set UBUNTU_VERSION=22.04
set NO_CACHE=false

REM Function to show usage
:show_usage
echo Usage: %~nx0 [OPTIONS]
echo.
echo Build Docker images for Remote Sensing Image Classification System
echo.
echo OPTIONS:
echo     /t TYPE         Build type: production, development, cpu, jupyter (default: production)
echo     /n NAME         Image name (default: remote-sensing-classification)
echo     /tag TAG        Image tag (default: latest)
echo     /p              Push to registry after build
echo     /r URL          Registry URL (required if /p is used)
echo     /python VER     Python version (default: 3.10)
echo     /cuda VER       CUDA version (default: 12.2.2)
echo     /ubuntu VER     Ubuntu version (default: 22.04)
echo     /no-cache       Build without using cache
echo     /h              Show this help message
echo.
echo EXAMPLES:
echo     %~nx0 /t production
echo     %~nx0 /t development /tag dev
echo     %~nx0 /t cpu /tag cpu-latest
echo     %~nx0 /t jupyter /tag jupyter-latest
echo     %~nx0 /t production /p /r your-registry.com
echo.
goto :eof

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :check_prerequisites
if /i "%~1"=="/t" (
    set BUILD_TYPE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/n" (
    set IMAGE_NAME=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/tag" (
    set TAG=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/p" (
    set PUSH_TO_REGISTRY=true
    shift
    goto :parse_args
)
if /i "%~1"=="/r" (
    set REGISTRY=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/python" (
    set PYTHON_VERSION=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/cuda" (
    set CUDA_VERSION=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/ubuntu" (
    set UBUNTU_VERSION=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="/no-cache" (
    set NO_CACHE=true
    shift
    goto :parse_args
)
if /i "%~1"=="/h" (
    call :show_usage
    exit /b 0
)
echo [ERROR] Unknown option: %~1
call :show_usage
exit /b 1

REM Function to check prerequisites
:check_prerequisites
echo [INFO] Checking prerequisites...

docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not in PATH
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker daemon is not running
    exit /b 1
)

echo [SUCCESS] Prerequisites check passed
goto :build_image

REM Function to build image
:build_image
set dockerfile=Dockerfile
set build_args=
set full_image_name=%IMAGE_NAME%:%TAG%

REM Add registry prefix if specified
if not "%REGISTRY%"=="" (
    set full_image_name=%REGISTRY%/%full_image_name%
)

REM Set dockerfile based on build type
if /i "%BUILD_TYPE%"=="production" (
    set dockerfile=Dockerfile
    set build_args=--target production
) else if /i "%BUILD_TYPE%"=="development" (
    set dockerfile=Dockerfile
    set build_args=--target development
) else if /i "%BUILD_TYPE%"=="cpu" (
    set dockerfile=Dockerfile.cpu
    set build_args=--target production
) else if /i "%BUILD_TYPE%"=="jupyter" (
    set dockerfile=Dockerfile.jupyter
    set build_args=
) else (
    echo [ERROR] Unknown build type: %BUILD_TYPE%
    exit /b 1
)

REM Add build arguments
set build_args=%build_args% --build-arg PYTHON_VERSION=%PYTHON_VERSION%
set build_args=%build_args% --build-arg CUDA_VERSION=%CUDA_VERSION%
set build_args=%build_args% --build-arg UBUNTU_VERSION=%UBUNTU_VERSION%

REM Add no-cache if specified
if /i "%NO_CACHE%"=="true" (
    set build_args=%build_args% --no-cache
)

echo [INFO] Building Docker image...
echo [INFO] Image name: %full_image_name%
echo [INFO] Dockerfile: %dockerfile%
echo [INFO] Build type: %BUILD_TYPE%
echo [INFO] Python version: %PYTHON_VERSION%

if not "%BUILD_TYPE%"=="cpu" (
    echo [INFO] CUDA version: %CUDA_VERSION%
)

REM Build the image
docker build -f %dockerfile% -t %full_image_name% %build_args% .

if errorlevel 1 (
    echo [ERROR] Failed to build Docker image
    exit /b 1
)

echo [SUCCESS] Docker image built successfully: %full_image_name%

REM Show image size
for /f "tokens=*" %%i in ('docker images --format "{{.Size}}" %full_image_name%') do set image_size=%%i
echo [INFO] Image size: %image_size%

goto :push_image

REM Function to push image to registry
:push_image
if /i "%PUSH_TO_REGISTRY%"=="true" (
    if "%REGISTRY%"=="" (
        echo [ERROR] Registry URL is required when pushing
        exit /b 1
    )
    
    set full_image_name=%REGISTRY%/%IMAGE_NAME%:%TAG%
    
    echo [INFO] Pushing image to registry: !full_image_name!
    docker push !full_image_name!
    
    if errorlevel 1 (
        echo [ERROR] Failed to push image
        exit /b 1
    )
    
    echo [SUCCESS] Image pushed successfully
)

goto :test_image

REM Function to run basic tests on the built image
:test_image
set full_image_name=%IMAGE_NAME%:%TAG%

if not "%REGISTRY%"=="" (
    set full_image_name=%REGISTRY%/%full_image_name%
)

echo [INFO] Running basic tests on the image...

if /i "%BUILD_TYPE%"=="jupyter" (
    echo [INFO] Testing Jupyter image startup...
    start /b docker run --rm -d --name test-container -p 8889:8888 %full_image_name%
    timeout /t 10 /nobreak >nul
    docker ps | findstr test-container >nul
    if not errorlevel 1 (
        echo [SUCCESS] Jupyter container started successfully
        docker stop test-container >nul
    ) else (
        echo [ERROR] Jupyter container failed to start
        exit /b 1
    )
) else (
    echo [INFO] Testing application startup...
    docker run --rm --name test-container %full_image_name% python -c "import src; print('Import successful')"
    if not errorlevel 1 (
        echo [SUCCESS] Application imports working correctly
    ) else (
        echo [ERROR] Application import test failed
        exit /b 1
    )
)

goto :main_end

REM Main execution
:main
echo [INFO] Starting Docker build process...
echo [INFO] Build type: %BUILD_TYPE%
echo [INFO] Image name: %IMAGE_NAME%
echo [INFO] Tag: %TAG%

call :parse_args %*

:main_end
echo [SUCCESS] Docker build process completed successfully!

REM Show final image information
set full_image_name=%IMAGE_NAME%:%TAG%
if not "%REGISTRY%"=="" (
    set full_image_name=%REGISTRY%/%full_image_name%
)

echo [INFO] Built image: %full_image_name%
echo [INFO] To run the image:

if /i "%BUILD_TYPE%"=="jupyter" (
    echo   docker run -p 8888:8888 %full_image_name%
) else (
    echo   docker run -p 8000:8000 %full_image_name%
)

exit /b 0

REM Start main execution
call :main %*
