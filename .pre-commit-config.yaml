# Pre-commit configuration for Remote Sensing Image Classification System
# Install with: pre-commit install
# Run manually: pre-commit run --all-files

repos:
  # Basic file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-toml
      - id: check-json
      - id: check-xml
      - id: check-added-large-files
        args: [--maxkb=10000]  # 10MB limit
      - id: check-merge-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: check-case-conflict
      - id: check-symlinks
      - id: destroyed-symlinks

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,E266,E501,W503]
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        args: [--ignore-missing-imports, --scripts-are-modules]
        additional_dependencies:
          - types-requests
          - types-Pillow
          - types-setuptools

  # Security scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/, -f, json, -o, bandit-report.json]
        exclude: ^tests/

  # Dependency scanning
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        args: [--ignore=51668]  # Ignore specific CVE if needed

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google]
        exclude: ^(tests/|setup.py)

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        types: [yaml]
        exclude: ^(.pre-commit-config.yaml|docker-compose.*\.yml)$

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.5
    hooks:
      - id: shellcheck

  # Jupyter notebook cleaning
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.0
    hooks:
      - id: nbqa-black
        args: [--line-length=88]
      - id: nbqa-isort
        args: [--profile=black]
      - id: nbqa-flake8
        args: [--max-line-length=88, --extend-ignore=E203,E266,E501,W503]

  # Remove notebook outputs
  - repo: https://github.com/kynan/nbstripout
    rev: 0.6.1
    hooks:
      - id: nbstripout

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: [--fix]

  # Requirements.txt sorting
  - repo: https://github.com/pre-commit/mirrors-pip-compile
    rev: v7.1.0
    hooks:
      - id: pip-compile
        files: ^requirements\.in$
        args: [--generate-hashes]

  # Python upgrade syntax
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.10.1
    hooks:
      - id: pyupgrade
        args: [--py38-plus]

  # Remove unused imports
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.2.0
    hooks:
      - id: autoflake
        args:
          - --in-place
          - --remove-all-unused-imports
          - --remove-unused-variables
          - --remove-duplicate-keys
          - --ignore-init-module-imports

  # Check for common security issues
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: package.lock.json

  # Local hooks for project-specific checks
  - repo: local
    hooks:
      # Check for TODO/FIXME comments
      - id: check-todos
        name: Check for TODO/FIXME comments
        entry: bash -c 'if grep -r "TODO\|FIXME" src/ --exclude-dir=__pycache__ --exclude="*.pyc"; then echo "Found TODO/FIXME comments. Please resolve before committing."; exit 1; fi'
        language: system
        pass_filenames: false

      # Check for print statements in production code
      - id: check-print-statements
        name: Check for print statements
        entry: bash -c 'if grep -r "print(" src/ --exclude-dir=__pycache__ --exclude="*.pyc" --exclude="debug.py"; then echo "Found print statements. Use logging instead."; exit 1; fi'
        language: system
        pass_filenames: false

      # Check for large files
      - id: check-large-files
        name: Check for large files
        entry: bash -c 'find . -type f -size +5M -not -path "./.git/*" -not -path "./outputs/*" -not -path "./data/*" | head -10'
        language: system
        pass_filenames: false

      # Validate configuration files
      - id: validate-config
        name: Validate configuration files
        entry: python -c "from src.config import config; print('Configuration validation passed')"
        language: system
        files: ^(src/config\.py|\.env\.example|pyproject\.toml)$
        pass_filenames: false

      # Check requirements consistency
      - id: check-requirements
        name: Check requirements consistency
        entry: bash -c 'python scripts/check_requirements.py'
        language: system
        files: ^requirements.*\.txt$
        pass_filenames: false

# Configuration
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.0.0

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
