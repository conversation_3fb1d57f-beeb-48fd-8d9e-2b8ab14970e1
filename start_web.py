#!/usr/bin/env python3
"""
启动脚本 - 遥感图像分类系统Web界面
Startup script for Remote Sensing Image Classification Web Interface
"""

import os
import sys
import subprocess
import argparse

def check_requirements():
    """检查必要的依赖是否已安装"""
    try:
        import fastapi
        import uvicorn
        import torch
        import torchvision
        import PIL
        print("✓ 所有必要依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r src/requirements.txt")
        return False

def check_models():
    """检查模型文件是否存在"""
    checkpoints_dir = "outputs/checkpoints"
    if os.path.exists(checkpoints_dir):
        print(f"✓ 找到模型目录: {checkpoints_dir}")
        return True
    else:
        print(f"⚠ 模型目录不存在: {checkpoints_dir}")
        print("请先运行训练脚本生成模型，或者系统将使用预训练权重")
        return False

def check_static_files():
    """检查静态文件是否存在"""
    static_files = [
        "static/index.html",
        "static/css/style.css", 
        "static/js/main.js"
    ]
    
    missing_files = []
    for file_path in static_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少静态文件: {missing_files}")
        return False
    else:
        print("✓ 所有静态文件存在")
        return True

def start_web_server(host="0.0.0.0", port=8000, reload=True):
    """启动Web服务器"""
    print(f"启动Web服务器...")
    print(f"访问地址: http://localhost:{port}")
    print(f"API文档: http://localhost:{port}/docs")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 切换到src目录
        os.chdir("src")
        
        # 启动uvicorn服务器
        cmd = [
            sys.executable, "-m", "uvicorn",
            "web_app:app",
            "--host", host,
            "--port", str(port),
            "--log-level", "info"
        ]
        
        if reload:
            cmd.append("--reload")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="启动遥感图像分类系统Web界面")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--no-reload", action="store_true", help="禁用自动重载")
    parser.add_argument("--skip-checks", action="store_true", help="跳过环境检查")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("遥感图像分类系统 - Web界面启动器")
    print("Remote Sensing Image Classification - Web Interface")
    print("=" * 60)
    
    if not args.skip_checks:
        print("\n1. 检查环境...")
        
        # 检查依赖
        if not check_requirements():
            sys.exit(1)
        
        # 检查静态文件
        if not check_static_files():
            print("请确保前端文件已正确创建")
            sys.exit(1)
        
        # 检查模型（非必须）
        check_models()
        
        print("\n2. 环境检查完成")
    
    print(f"\n3. 启动Web服务器...")
    start_web_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload
    )

if __name__ == "__main__":
    main()
