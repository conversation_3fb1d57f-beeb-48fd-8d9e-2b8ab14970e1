"""
Demo FastAPI Web Application for Remote Sensing Image Classification
Provides HTML/CSS/JS frontend with mock data for demonstration
"""

import os
import json
import random
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from PIL import Image
import io
import time

# Initialize FastAPI app
app = FastAPI(
    title="遥感图像分类系统 (演示版)",
    description="Remote Sensing Image Classification System Demo",
    version="1.0.0-demo"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="../static"), name="static")

# Mock data
DEMO_CLASSES = [
    "airplane", "baseball_field", "basketball_court", "beach", "bridge",
    "chaparral", "dense_residential", "forest", "freeway", "golf_course",
    "harbor", "intersection", "mobile_home_park", "overpass", "parking_lot",
    "railway", "river", "runway", "sparse_residential", "storage_tank", "tennis_court"
]

DEMO_MODELS = [
    {"key": "resnet50-原始", "name": "ResNet50 (原始)", "type": "resnet50", "variant": "original"},
    {"key": "resnet50-剪枝", "name": "ResNet50 (剪枝)", "type": "resnet50", "variant": "pruned"},
    {"key": "resnet50-蒸馏", "name": "ResNet50 (蒸馏)", "type": "resnet50", "variant": "distilled"},
    {"key": "resnet50-量化", "name": "ResNet50 (量化)", "type": "resnet50", "variant": "quantized"},
    {"key": "densenet201-原始", "name": "DenseNet201 (原始)", "type": "densenet201", "variant": "original"},
    {"key": "vit_s_16-原始", "name": "ViT-S/16 (原始)", "type": "vit_s_16", "variant": "original"},
    {"key": "swin_t-原始", "name": "Swin-T (原始)", "type": "swin_t", "variant": "original"},
]

# Global state
monitoring_active = False
fine_tuning_status = "等待中..."

@app.on_event("startup")
async def startup_event():
    """Initialize the demo application"""
    print("Initializing Demo Remote Sensing Image Classification System...")
    print("Demo mode - using mock data")
    print("System ready!")

# Root endpoint - serve the main HTML page
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    try:
        with open("../static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Frontend not found")

# API Endpoints

@app.get("/api/models")
async def get_available_models():
    """Get list of available models (demo)"""
    return {"models": DEMO_MODELS}

@app.post("/api/predict")
async def predict_image(
    image: UploadFile = File(...),
    model_key: str = Form(...)
):
    """Predict image classification (demo with mock results)"""
    try:
        # Validate file type
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Invalid file type")
        
        # Simulate processing time
        await asyncio.sleep(random.uniform(0.5, 2.0))
        
        # Generate mock predictions
        selected_classes = random.sample(DEMO_CLASSES, 5)
        confidences = sorted([random.uniform(0.1, 0.9) for _ in range(5)], reverse=True)
        
        predictions = {cls: conf for cls, conf in zip(selected_classes, confidences)}
        
        # Mock inference time
        inference_time = f"推理时间: {random.uniform(10, 100):.2f} ms"
        
        return {
            "predictions": predictions,
            "inference_time": inference_time,
            "status": "success"
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/api/comparison/{comparison_type}/{model_type}")
async def get_comparison_table(comparison_type: str, model_type: str):
    """Get model comparison table (demo)"""
    try:
        # Generate mock comparison table
        if comparison_type == "pruning":
            html_content = f"""
            <h3>{model_type.upper()} 剪枝模型与原始模型比较</h3>
            <table class="pure-table pure-table-striped">
                <thead>
                    <tr>
                        <th>评估指标</th>
                        <th>{model_type} (原始)</th>
                        <th>{model_type} (剪枝)</th>
                        <th>相对变化</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>准确率</td><td>0.8500</td><td>0.8200</td><td>-3.53%</td></tr>
                    <tr><td>精确率</td><td>0.8400</td><td>0.8100</td><td>-3.57%</td></tr>
                    <tr><td>召回率</td><td>0.8300</td><td>0.8000</td><td>-3.61%</td></tr>
                    <tr><td>F1分数</td><td>0.8400</td><td>0.8100</td><td>-3.57%</td></tr>
                    <tr><td>非零参数量</td><td>25,000,000</td><td>12,500,000</td><td>50.00%</td></tr>
                    <tr><td>稀疏度</td><td>0.0000</td><td>0.5000</td><td>-</td></tr>
                </tbody>
            </table>
            """
        elif comparison_type == "distillation":
            html_content = f"""
            <h3>{model_type.upper()} 蒸馏模型与原始模型比较</h3>
            <table class="pure-table pure-table-striped">
                <thead>
                    <tr>
                        <th>评估指标</th>
                        <th>{model_type} (原始)</th>
                        <th>{model_type} (蒸馏 - MobileNetV2)</th>
                        <th>相对变化</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>准确率</td><td>0.8500</td><td>0.7800</td><td>-8.24%</td></tr>
                    <tr><td>精确率</td><td>0.8400</td><td>0.7700</td><td>-8.33%</td></tr>
                    <tr><td>召回率</td><td>0.8300</td><td>0.7600</td><td>-8.43%</td></tr>
                    <tr><td>F1分数</td><td>0.8400</td><td>0.7700</td><td>-8.33%</td></tr>
                    <tr><td>参数量</td><td>25,000,000</td><td>3,400,000</td><td>86.40%</td></tr>
                    <tr><td>模型大小(MB)</td><td>95.20</td><td>13.60</td><td>85.71%</td></tr>
                </tbody>
            </table>
            """
        elif comparison_type == "quantization":
            html_content = f"""
            <h3>{model_type.upper()} 量化模型与原始模型比较</h3>
            <table class="pure-table pure-table-striped">
                <thead>
                    <tr>
                        <th>评估指标</th>
                        <th>{model_type} (原始)</th>
                        <th>{model_type} (量化)</th>
                        <th>相对变化</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>准确率</td><td>0.8500</td><td>0.8400</td><td>-1.18%</td></tr>
                    <tr><td>精确率</td><td>0.8400</td><td>0.8300</td><td>-1.19%</td></tr>
                    <tr><td>召回率</td><td>0.8300</td><td>0.8200</td><td>-1.20%</td></tr>
                    <tr><td>F1分数</td><td>0.8400</td><td>0.8300</td><td>-1.19%</td></tr>
                    <tr><td>模型大小(MB)</td><td>95.20</td><td>23.80</td><td>75.00%</td></tr>
                </tbody>
            </table>
            """
        else:
            raise HTTPException(status_code=400, detail="Invalid comparison type")
        
        return {"html": html_content}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@app.get("/api/dataset/samples")
async def get_dataset_samples():
    """Get dataset sample images (demo)"""
    try:
        # Return empty arrays for demo
        return {
            "old_images": [],
            "old_info": [],
            "new_images": [],
            "new_info": []
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Dataset loading failed: {str(e)}")

@app.get("/api/dataset/image/{image_path:path}")
async def get_dataset_image(image_path: str):
    """Serve dataset images (demo)"""
    raise HTTPException(status_code=404, detail="Demo mode - no images available")

# Adaptive Fine-tuning Endpoints (Demo)

@app.post("/api/adaptive/start")
async def start_monitoring():
    """Start adaptive monitoring (demo)"""
    global monitoring_active
    monitoring_active = True
    return {"message": "自适应监控已启动 (演示模式)", "status": "success"}

@app.post("/api/adaptive/stop")
async def stop_monitoring():
    """Stop adaptive monitoring (demo)"""
    global monitoring_active
    monitoring_active = False
    return {"message": "自适应监控已停止", "status": "success"}

@app.post("/api/adaptive/manual-finetune")
async def trigger_manual_finetune():
    """Trigger manual fine-tuning (demo)"""
    global fine_tuning_status
    fine_tuning_status = "微调已启动 (演示模式)..."
    return {"message": "手动微调已启动 (演示模式)", "status": "success"}

@app.get("/api/adaptive/status")
async def get_adaptive_status():
    """Get adaptive fine-tuning status (demo)"""
    return {"fine_tuning_status": fine_tuning_status}

@app.get("/api/adaptive/check-distribution")
async def check_distribution():
    """Check data distribution (demo)"""
    # Generate random distribution data
    diff_score = random.uniform(0.05, 0.3)
    needs_fine_tuning = diff_score > 0.1
    
    return {
        "needs_fine_tuning": needs_fine_tuning,
        "difference_score": diff_score,
        "status": "success"
    }

# Health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy (demo mode)",
        "models_loaded": len(DEMO_MODELS),
        "classes_count": len(DEMO_CLASSES)
    }

if __name__ == "__main__":
    import asyncio
    
    print("Starting Remote Sensing Image Classification Demo Web Application...")
    print("Access the application at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    print("Note: This is a demo version with mock data")
    
    uvicorn.run(
        "demo_web_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
