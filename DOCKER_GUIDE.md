# Docker 部署指南

## 概述

本项目提供了多种 Docker 配置，支持不同的部署场景和开发需求。

## 🐳 Docker 镜像类型

### 1. 生产环境镜像 (Production)
- **文件**: `Dockerfile`
- **目标**: `production`
- **特点**: 优化的生产环境，包含 CUDA 支持
- **用途**: 生产部署，GPU 推理

### 2. 开发环境镜像 (Development)
- **文件**: `Dockerfile`
- **目标**: `development`
- **特点**: 包含开发工具，支持热重载
- **用途**: 本地开发，调试

### 3. CPU 专用镜像 (CPU-only)
- **文件**: `Dockerfile.cpu`
- **特点**: 轻量级，仅 CPU 推理
- **用途**: 无 GPU 环境部署

### 4. Jupyter 开发镜像
- **文件**: `Dockerfile.jupyter`
- **特点**: 完整的数据科学环境
- **用途**: 模型开发，数据分析

## 🚀 快速开始

### 构建镜像

#### 使用构建脚本 (推荐)

**Linux/macOS:**
```bash
# 生产环境镜像
./scripts/build-docker.sh --type production

# 开发环境镜像
./scripts/build-docker.sh --type development --tag dev

# CPU 专用镜像
./scripts/build-docker.sh --type cpu --tag cpu-latest

# Jupyter 镜像
./scripts/build-docker.sh --type jupyter --tag jupyter-latest
```

**Windows:**
```cmd
# 生产环境镜像
scripts\build-docker.bat /t production

# 开发环境镜像
scripts\build-docker.bat /t development /tag dev

# CPU 专用镜像
scripts\build-docker.bat /t cpu /tag cpu-latest

# Jupyter 镜像
scripts\build-docker.bat /t jupyter /tag jupyter-latest
```

#### 手动构建

```bash
# 生产环境镜像
docker build --target production -t rsic:production .

# 开发环境镜像
docker build --target development -t rsic:dev .

# CPU 专用镜像
docker build -f Dockerfile.cpu -t rsic:cpu .

# Jupyter 镜像
docker build -f Dockerfile.jupyter -t rsic:jupyter .
```

### 运行容器

#### 生产环境
```bash
# 基本运行
docker run -p 8000:8000 rsic:production

# 挂载数据目录
docker run -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/outputs:/app/outputs \
  rsic:production

# 环境变量配置
docker run -p 8000:8000 \
  -e ENVIRONMENT=production \
  -e LOG_LEVEL=INFO \
  --env-file .env \
  rsic:production
```

#### 开发环境
```bash
# 开发模式（热重载）
docker run -p 8000:8000 \
  -v $(pwd):/app \
  -e DEBUG=true \
  -e RELOAD=true \
  rsic:dev

# 交互式开发
docker run -it -p 8000:8000 \
  -v $(pwd):/app \
  rsic:dev bash
```

#### Jupyter 环境
```bash
# 启动 Jupyter Lab
docker run -p 8888:8888 \
  -v $(pwd)/notebooks:/app/notebooks \
  -v $(pwd)/data:/app/data \
  -e JUPYTER_TOKEN=your-secure-token \
  rsic:jupyter

# 访问: http://localhost:8888
```

#### GPU 支持
```bash
# 使用 NVIDIA Docker
docker run --gpus all -p 8000:8000 rsic:production

# 指定特定 GPU
docker run --gpus device=0 -p 8000:8000 rsic:production
```

## 🔧 Docker Compose 部署

### 开发环境
```bash
# 启动开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# 后台运行
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 查看日志
docker-compose logs -f app
```

### 生产环境
```bash
# 启动生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 扩展应用实例
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale app=3

# 更新服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml pull
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 监控环境
```bash
# 启动完整监控栈
docker-compose --profile monitoring up -d

# 访问服务
# - 应用: http://localhost:8000
# - Grafana: http://localhost:3000
# - Prometheus: http://localhost:9090
# - MLflow: http://localhost:5000
```

## 📁 目录挂载

### 推荐的挂载点

```bash
docker run -p 8000:8000 \
  -v $(pwd)/data:/app/data \              # 数据目录
  -v $(pwd)/outputs:/app/outputs \        # 输出目录
  -v $(pwd)/logs:/app/logs \              # 日志目录
  -v $(pwd)/static:/app/static \          # 静态文件
  -v $(pwd)/.env:/app/.env \              # 环境配置
  rsic:production
```

### 开发环境挂载
```bash
docker run -p 8000:8000 \
  -v $(pwd):/app \                        # 整个项目目录
  -v /app/__pycache__ \                   # 排除缓存目录
  -v /app/.pytest_cache \                 # 排除测试缓存
  rsic:dev
```

## 🔐 安全配置

### 非 root 用户
所有镜像都使用非 root 用户运行：
- **用户**: `appuser` (UID: 1000)
- **组**: `appuser` (GID: 1000)

### 环境变量安全
```bash
# 使用环境文件
docker run --env-file .env.prod rsic:production

# 避免在命令行暴露敏感信息
docker run -e SECRET_KEY_FILE=/run/secrets/secret_key rsic:production
```

### 网络安全
```bash
# 创建自定义网络
docker network create rsic-network

# 在自定义网络中运行
docker run --network rsic-network rsic:production
```

## 📊 监控和日志

### 健康检查
所有生产镜像都包含健康检查：
```bash
# 查看健康状态
docker ps
docker inspect <container_id> | grep Health -A 10
```

### 日志管理
```bash
# 查看日志
docker logs <container_id>

# 实时日志
docker logs -f <container_id>

# 限制日志大小
docker run --log-opt max-size=10m --log-opt max-file=3 rsic:production
```

### 资源监控
```bash
# 查看资源使用
docker stats

# 限制资源使用
docker run -m 2g --cpus="1.5" rsic:production
```

## 🔧 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 检查文件权限
ls -la data/ outputs/

# 修复权限
sudo chown -R 1000:1000 data/ outputs/
```

#### 2. GPU 不可用
```bash
# 检查 NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi

# 检查 GPU 驱动
nvidia-smi
```

#### 3. 内存不足
```bash
# 增加内存限制
docker run -m 4g rsic:production

# 使用 CPU 镜像
docker run rsic:cpu
```

#### 4. 端口冲突
```bash
# 使用不同端口
docker run -p 8001:8000 rsic:production

# 检查端口使用
netstat -tulpn | grep 8000
```

### 调试技巧

#### 进入容器调试
```bash
# 进入运行中的容器
docker exec -it <container_id> bash

# 以 root 用户进入
docker exec -it --user root <container_id> bash

# 运行调试容器
docker run -it --entrypoint bash rsic:production
```

#### 查看构建过程
```bash
# 详细构建日志
docker build --progress=plain --no-cache .

# 查看镜像层
docker history rsic:production
```

## 🚀 性能优化

### 构建优化
```bash
# 使用 BuildKit
export DOCKER_BUILDKIT=1
docker build .

# 多阶段构建缓存
docker build --target dependencies .
```

### 运行时优化
```bash
# 使用更多 worker
docker run -e WORKERS=4 rsic:production

# 优化内存设置
docker run -e OMP_NUM_THREADS=4 -e MKL_NUM_THREADS=4 rsic:production
```

### 镜像优化
```bash
# 清理未使用的镜像
docker image prune

# 查看镜像大小
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

## 📋 最佳实践

### 1. 镜像管理
- 使用语义化版本标签
- 定期更新基础镜像
- 清理未使用的镜像和容器

### 2. 安全实践
- 不在镜像中包含敏感信息
- 使用非 root 用户运行
- 定期扫描安全漏洞

### 3. 性能实践
- 合理设置资源限制
- 使用多阶段构建减小镜像大小
- 优化 Dockerfile 层缓存

### 4. 运维实践
- 实施健康检查
- 配置日志轮转
- 监控资源使用情况

## 🔗 相关链接

- [Docker 官方文档](https://docs.docker.com/)
- [NVIDIA Docker 文档](https://github.com/NVIDIA/nvidia-docker)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [项目配置文档](PROJECT_DEPENDENCIES_UPDATE.md)
