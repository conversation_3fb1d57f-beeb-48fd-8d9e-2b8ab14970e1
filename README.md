# 遥感图像分类系统

一个现代化的遥感图像分类系统，支持多种深度学习模型和模型优化技术，提供直观的 Web 界面和强大的 API 接口。

## ✨ 主要特性

### 🎯 核心功能

- **多模型支持**: ResNet50、DenseNet201、ViT、Swin Transformer
- **模型优化**: 剪枝、知识蒸馏、量化压缩
- **自适应微调**: 自动检测数据分布变化并触发模型更新
- **现代化界面**: 基于 FastAPI + HTML/CSS/JS 的响应式 Web 界面

### 🖼️ 图像分类

- 支持 21 个遥感场景类别的自动分类
- 实时预测结果和置信度显示
- 拖拽上传，直观易用

### 📊 性能分析

- 模型性能对比可视化
- 压缩效果分析
- 推理时间统计

### 🔧 开发友好

- RESTful API 接口
- Docker 容器化部署
- 完整的开发文档

## 🚀 快速开始

### 使用 Docker 部署（推荐）

#### 1. 构建镜像

```bash
# 生产环境镜像
docker build --target production -t rsic:latest .

# 开发环境镜像（支持热重载）
docker build --target development -t rsic:dev .
```

#### 2. 运行容器

```bash
# 生产环境运行
docker run -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/outputs:/app/outputs \
  rsic:latest

# 开发环境运行
docker run -p 8000:8000 \
  -v $(pwd):/app \
  -e DEBUG=true \
  rsic:dev
```

#### 3. 访问应用

- Web 界面: http://localhost:8000
- API 文档: http://localhost:8000/docs

### 本地开发部署

#### 1. 环境配置

```bash
# 创建虚拟环境（推荐）
conda create -n remote_sensing python=3.10
conda activate remote_sensing

# 安装依赖
pip install -r requirements.txt
```

#### 2. 启动应用

```bash
# 启动Web应用
python src/web_app.py

# 或启动Gradio界面（备选）
python src/app.py
```

## 📁 数据集说明

项目使用位于根目录下的 `old_dataset` 文件夹中的遥感场景数据集，包含 21 个类别：

**场景类别**:

- airplane (飞机) | baseball_field (棒球场) | basketball_court (篮球场) | beach (海滩)
- bridge (桥梁) | chaparral (灌木丛) | dense_residential (密集住宅区) | forest (森林)
- freeway (高速公路) | golf_course (高尔夫球场) | harbor (港口) | intersection (十字路口)
- mobile_home_park (移动房屋公园) | overpass (立交桥) | parking_lot (停车场) | railway (铁路)
- river (河流) | runway (跑道) | sparse_residential (稀疏住宅区) | storage_tank (储油罐) | tennis_court (网球场)

数据集自动按照 7:1.5:1.5 的比例划分为训练集、验证集和测试集。

## 📂 项目结构

```
yaogan-zsy/
├── src/                      # 核心源代码
│   ├── web_app.py           # 🌟 FastAPI Web应用服务器（主要后端）
│   ├── app.py               # Gradio界面（备选界面）
│   ├── main.py              # 自动化训练流程控制
│   ├── train.py             # 模型训练脚本
│   ├── evaluate.py          # 模型评估脚本
│   ├── prune.py             # 模型剪枝脚本
│   ├── quanti.py            # 模型量化脚本
│   ├── compare.py           # 模型对比分析
│   ├── data/                # 数据处理模块
│   ├── models/              # 模型定义
│   ├── optimization/        # 模型优化技术
│   └── utils/               # 工具函数
├── static/                   # 🌟 Web前端（主要界面）
│   ├── index.html           # 主页面
│   ├── css/style.css        # 扁平化样式
│   ├── js/main.js           # 交互逻辑
│   └── README.md            # 前端文档
├── old_dataset/             # 训练数据集（21个类别）
├── new_dataset/             # 新数据（用于自适应微调）
├── outputs/                 # 模型输出和结果
│   └── run_YYYYMMDD_HHMMSS/ # 时间戳运行目录
│       ├── resnet50/        # 各模型的训练结果
│       ├── densenet201/
│       ├── vit_s_16/
│       └── swin_t/
├── imgs/                    # 上传图像临时存储
├── Dockerfile               # Docker构建文件
├── requirements.txt         # Python依赖
└── README.md               # 项目文档
```

## 🌐 Web 界面使用

### 现代化 Web 界面（推荐）

基于 FastAPI + HTML/CSS/JS 构建的现代化界面，提供最佳用户体验。

#### 功能特性

- **🖼️ 图像预测**: 拖拽上传，多模型选择，实时结果显示
- **📊 模型对比**: 剪枝、蒸馏、量化效果对比可视化
- **🗂️ 数据集可视化**: 训练和新数据集样本展示
- **⚙️ 自适应微调**: 自动监控、手动触发、实时状态

#### 界面访问

```bash
# 启动服务
python src/web_app.py

# 访问地址
http://localhost:8000        # 主界面
http://localhost:8000/docs   # API文档
```

#### 使用说明

1. **图像预测页面**: 上传遥感图像，选择模型进行分类预测
2. **模型对比页面**: 查看不同优化技术的性能对比
3. **数据集页面**: 浏览训练数据和新数据样本
4. **自适应微调页面**: 配置和控制自动微调功能

### Gradio 界面（备选）

传统的 Gradio 图形界面，适合快速测试。

```bash
# 启动Gradio界面
python src/app.py
```

## 🤖 支持的模型

### 主要模型架构

- **ResNet50** - 经典卷积神经网络（默认推荐）
- **DenseNet201** - 密集连接网络
- **ViT-S/16** - Vision Transformer 小型版本
- **Swin-T** - Swin Transformer 微型版本

### 学生模型（用于知识蒸馏）

- **MobileNetV2** - 轻量级移动端模型
- **ResNet18** - 轻量级 ResNet 版本

### 模型特点

- 支持 ImageNet 预训练权重
- 自动适配 21 类遥感场景分类
- 支持 GPU 和 CPU 推理
- 懒加载机制，按需加载模型

## 🔧 模型训练与优化

### 自动化训练流程

使用 `main.py` 进行自动化的模型训练、评估和优化。

#### 基本用法

```bash
# 训练单个模型
python src/main.py --model_names resnet50 --epochs 20

# 训练多个模型并进行优化
python src/main.py \
    --model_names resnet50 swin_t \
    --pretrained \
    --epochs 20 \
    --optimize_mode all \
    --pruning_methods global \
    --pruning_ratios 0.5 \
    --fine_tune_epochs 10 \
    --student_model mobilenetv2 \
    --distill_epochs 30
```

#### 关键参数说明

- `--model_names`: 要训练的模型列表（resnet50, densenet201, vit_s_16, swin_t）
- `--pretrained`: 使用 ImageNet 预训练权重（推荐）
- `--epochs`: 训练轮数
- `--optimize_mode`: 优化模式（none, prune, distill, quantize, all）
- `--output_dir`: 输出目录（默认：outputs）

### 单独使用各个脚本

#### 模型训练

```bash
python src/train.py --model_name resnet50 --epochs 20 --pretrained
```

#### 模型评估

```bash
python src/evaluate.py --model_path outputs/run_xxx/resnet50/model.pth
```

#### 模型剪枝

```bash
python src/prune.py --model_path outputs/run_xxx/resnet50/model.pth --pruning_ratio 0.5
```

#### 模型量化

```bash
python src/quanti.py --run_directory outputs/run_xxx
```

## 📡 API 接口文档

### 核心 API 端点

#### 图像预测

```http
POST /api/predict
Content-Type: multipart/form-data

参数:
- image: 图像文件 (JPG/PNG)
- model_key: 模型标识符

响应:
{
  "predictions": {"类别1": 0.85, "类别2": 0.12, ...},
  "inference_time": 0.123,
  "status": "success"
}
```

#### 获取可用模型

```http
GET /api/models

响应:
{
  "models": [
    {"key": "resnet50", "name": "ResNet50", "type": "original"},
    {"key": "resnet50_pruned", "name": "ResNet50", "type": "pruned"},
    ...
  ]
}
```

#### 模型对比数据

```http
GET /api/comparison/{comparison_type}/{model_type}

参数:
- comparison_type: pruning/distillation/quantization
- model_type: resnet50/densenet201/vit_s_16/swin_t

响应:
{
  "html": "<table>...</table>"
}
```

### 自适应微调 API

#### 启动/停止监控

```http
POST /api/adaptive/start
POST /api/adaptive/stop

响应:
{
  "message": "监控已启动/停止",
  "status": "success"
}
```

#### 手动触发微调

```http
POST /api/adaptive/manual-finetune

响应:
{
  "message": "微调已触发",
  "status": "success"
}
```

#### 检查系统状态

```http
GET /api/adaptive/status
GET /api/adaptive/check-distribution
GET /api/health
```

### 使用示例

```python
import requests

# 图像预测
with open('image.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/api/predict',
        files={'image': f},
        data={'model_key': 'resnet50'}
    )
    result = response.json()
    print(result['predictions'])

# 获取模型列表
response = requests.get('http://localhost:8000/api/models')
models = response.json()['models']
```

## 🐳 Docker 部署指南

### 快速部署

#### 1. 构建镜像

```bash
# 生产环境镜像（推荐）
docker build --target production -t rsic:latest .

# 开发环境镜像
docker build --target development -t rsic:dev .

# CPU专用镜像（无GPU环境）
docker build -f Dockerfile.cpu -t rsic:cpu .
```

#### 2. 运行容器

```bash
# 生产环境运行
docker run -d \
  --name rsic-app \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/outputs:/app/outputs \
  -v $(pwd)/old_dataset:/app/old_dataset \
  -v $(pwd)/new_dataset:/app/new_dataset \
  rsic:latest

# 开发环境运行（支持热重载）
docker run -d \
  --name rsic-dev \
  -p 8000:8000 \
  -v $(pwd):/app \
  -e DEBUG=true \
  -e RELOAD=true \
  rsic:dev

# GPU支持
docker run --gpus all -p 8000:8000 rsic:latest
```

#### 3. 容器管理

```bash
# 查看运行状态
docker ps

# 查看日志
docker logs rsic-app

# 进入容器
docker exec -it rsic-app bash

# 停止容器
docker stop rsic-app

# 删除容器
docker rm rsic-app
```

### 环境变量配置

```bash
# 创建环境文件
cat > .env << EOF
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
WORKERS=4
EOF

# 使用环境文件运行
docker run --env-file .env -p 8000:8000 rsic:latest
```

### 数据持久化

```bash
# 创建数据卷
docker volume create rsic-data
docker volume create rsic-outputs

# 使用数据卷运行
docker run -d \
  -p 8000:8000 \
  -v rsic-data:/app/data \
  -v rsic-outputs:/app/outputs \
  rsic:latest
```

### 健康检查

容器内置健康检查，可通过以下方式查看：

```bash
# 查看健康状态
docker inspect rsic-app | grep Health -A 10

# 手动健康检查
curl http://localhost:8000/api/health
```

> 💡 **提示**: 详细的 Docker 配置和故障排除请参考 [DOCKER_GUIDE.md](DOCKER_GUIDE.md)

## ⚙️ 自适应微调功能

项目提供智能的自适应微调功能，能够自动监控数据分布变化并触发模型更新。

### 功能特性

- **🔍 自动监控**: 每 10 分钟检测`new_dataset`目录下的新数据
- **📊 分布检测**: 使用多种方法计算数据分布差异
- **🔄 自动微调**: 当差异超过阈值时自动触发模型微调
- **🎯 知识蒸馏**: 微调完成后自动进行知识蒸馏
- **🎛️ 手动控制**: 支持手动触发微调和分布检查

### 使用方法

#### 1. 准备新数据

将新的遥感图像放入对应的类别目录：

```
new_dataset/
├── airplane/        # 新的飞机图像
├── beach/          # 新的海滩图像
├── bridge/         # 新的桥梁图像
└── ...
```

#### 2. 启动监控

通过 Web 界面的"自适应微调"页面：

- 点击"启动自适应监控"开始监控
- 调整"分布差异阈值"控制触发敏感度（默认 0.1）
- 点击"手动触发微调"立即执行微调
- 点击"检查数据分布"查看当前分布差异

#### 3. 监控工作流程

1. **特征提取**: 从训练数据和新数据中提取特征向量
2. **差异计算**: 计算两个数据集的分布差异分数
3. **阈值判断**: 如果差异超过设定阈值，触发微调
4. **数据合并**: 将新数据与旧数据合并成训练集
5. **模型微调**: 使用合并数据集对原始模型进行微调
6. **权重替换**: 将微调后的权重替换原模型文件
7. **知识蒸馏**: 自动执行蒸馏过程生成轻量化模型
8. **数据清理**: 清空`new_dataset`等待下一批新数据

### 配置参数

- **分布差异阈值**: 0.01-0.5，默认 0.1，可在界面中调整
- **微调轮数**: 默认 5 轮，适合增量学习
- **学习率**: 默认 1e-4，适合微调任务
- **蒸馏轮数**: 默认 10 轮，自动执行

## 🔧 故障排除

### 常见问题

#### 1. 模型加载失败

```bash
# 检查模型文件是否存在
ls -la outputs/checkpoints/

# 检查权限
chmod 644 outputs/checkpoints/*.pth
```

#### 2. 图像预测错误

- 确保图像格式为 JPG/PNG
- 检查图像尺寸不要过大（建议<10MB）
- 验证模型是否正确加载

#### 3. Docker 容器启动失败

```bash
# 检查端口占用
netstat -tulpn | grep 8000

# 查看容器日志
docker logs rsic-app

# 检查磁盘空间
df -h
```

#### 4. GPU 不可用

```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查CUDA版本
nvcc --version

# 使用CPU版本
docker build -f Dockerfile.cpu -t rsic:cpu .
```

### 性能优化建议

- **模型优化**: 剪枝后进行微调通常能显著恢复精度
- **蒸馏调优**: 调整温度和 alpha 参数平衡精度和性能
- **内存管理**: 使用懒加载模式减少内存占用
- **阈值设置**: 合理设置分布差异阈值平衡更新频率和计算资源

## 📚 技术文档

### 相关文档

- [Docker 部署详细指南](DOCKER_GUIDE.md)
- [前端开发文档](static/README.md)
- [扁平化设计指南](FLAT_DESIGN_IMPLEMENTATION.md)
- [JavaScript 增强说明](JAVASCRIPT_ENHANCEMENT_SUMMARY.md)
- [项目依赖更新](PROJECT_DEPENDENCIES_UPDATE.md)

### 技术栈

- **后端**: FastAPI, PyTorch, timm
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **部署**: Docker, Gunicorn, Uvicorn
- **模型**: ResNet, DenseNet, ViT, Swin Transformer

## 📝 注意事项

### 模型限制

1. 静态量化主要对 ResNet 类模型效果较好，ViT/Swin 可能不适用
2. 剪枝 Transformer 模型可能需要特定的方法
3. 知识蒸馏相对通用，可尝试不同的教师/学生组合
4. 训练多个模型需要较长时间和大量计算资源

### 训练建议

- **Transformer 模型**: 强烈建议使用 ImageNet 预训练权重微调
- **学习率**: 微调时使用较小学习率（1e-4 或 1e-5）
- **训练轮数**: 预训练模型微调建议 20-50 轮
- **数据增强**: 可添加更多遥感图像特定的数据增强方法

### 部署注意

- 确保有足够的磁盘空间存储模型和数据
- GPU 环境需要正确安装 NVIDIA 驱动和 CUDA
- 生产环境建议使用 Docker 部署
- 定期备份训练结果和模型文件

## 🚀 未来规划

- [ ] 支持更多 Transformer 模型的量化优化
- [ ] 添加模型性能监控和告警
- [ ] 集成 MLflow 进行实验管理
- [ ] 支持分布式训练
- [ ] 添加更多遥感场景类别
- [ ] 优化移动端推理性能

---

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 联系方式

如有问题或建议，请通过 GitHub Issues 联系我们。
