# 遥感图像分类项目

本项目用于对遥感场景数据集中的遥感图像进行分类，支持多种模型架构（包括 ResNet50、DenseNet201、ViT、Swin Transformer）以及模型压缩技术（剪枝、蒸馏、量化）。

## 数据集说明

项目使用位于根目录下的 `old_dataset` 文件夹中的遥感场景数据集。该数据集包含 21 个类别：

- airplane (飞机)
- baseball_field (棒球场)
- basketball_court (篮球场)
- beach (海滩)
- bridge (桥梁)
- chaparral (灌木丛)
- dense_residential (密集住宅区)
- forest (森林)
- freeway (高速公路)
- golf_course (高尔夫球场)
- harbor (港口)
- intersection (十字路口)
- mobile_home_park (移动房屋公园)
- overpass (立交桥)
- parking_lot (停车场)
- railway (铁路)
- river (河流)
- runway (跑道)
- sparse_residential (稀疏住宅区)
- storage_tank (储油罐)
- tennis_court (网球场)

数据集自动按照 7:1.5:1.5 的比例划分为训练集、验证集和测试集。

## 项目结构

```
src/
├── data/            # 数据处理相关代码
├── models/          # 模型定义 (base_model.py等)
├── optimization/    # 模型压缩方法实现 (pruning.py, distillation.py, quantization.py)
├── utils/           # 工具函数 (metrics.py, visualization.py)
├── train.py         # 训练辅助脚本 (train_model 函数)
├── evaluate.py      # 评估辅助脚本 (evaluate_model 函数)
├── prune.py         # 剪枝微调辅助脚本 (fine_tune 函数)
├── quanti.py        # 独立量化脚本
├── compare.py       # 模型参数比较脚本
├── main.py          # 主流程控制脚本 (自动化训练、评估、剪枝、蒸馏多个模型)
├── app.py           # Gradio交互式演示界面
├── web_app.py       # FastAPI Web应用服务器
└── requirements.txt # Python依赖
static/              # Web前端静态文件
├── index.html       # 主页面
├── css/
│   └── style.css    # 样式文件
├── js/
│   └── main.js      # JavaScript逻辑
└── README.md        # 前端说明文档
README.md            # 项目说明
imgs/                # 保存上传的图像和临时文件
outputs/             # 训练和评估的输出目录 (run_YYYYMMDD_HHMMSS 子目录)
├── run_YYYYMMDD_HHMMSS/
│   ├── global_args.txt       # 全局运行参数
│   ├── resnet50/             # ResNet50 的输出
│   │   ├── model.pth         # 训练后的模型
│   │   ├── evaluation_results.txt # 评估结果
│   │   ├── args.txt          # 训练参数
│   │   ├── training_curves.png # 训练曲线
│   │   ├── pruned/           # 剪枝结果
│   │   │   └── global_50/
│   │   │       ├── pruned_global_50.pth
│   │   │       └── evaluation_results.txt
│   │   └── distilled/        # 蒸馏结果
│   │       ├── student_mobilenetv2.pth
│   │       └── evaluation_results.txt
│   ├── densenet201/          # DenseNet201 的输出
│   │   └── ...
│   ├── vit_s_16/             # ViT-S/16 的输出
│   │   └── ...
│   └── swin_t/               # Swin-T 的输出
│       └── ...
└── ...
```

## 支持的模型

- ResNet50 (默认)
- DenseNet201
- ViT-S/16 (`vit_small_patch16_224` 来自 timm 库)
- Swin-T (`swin_tiny_patch4_window7_224` 来自 timm 库)
- MobileNetV2 (作为蒸馏的学生模型)
- ResNet18 (作为蒸馏的学生模型)

## 使用说明

**核心脚本: `main.py`**

`main.py` 现在是自动化的主要入口点，用于依次处理一系列模型的训练、评估和优化（剪枝、蒸馏）。

### 环境配置

```bash
# 建议使用conda创建虚拟环境
# conda create -n remote_sensing python=3.10
# conda activate remote_sensing

pip install -r src/requirements.txt
```

### 自动化训练与优化 (`main.py`)

运行 `main.py` 将按顺序处理 `--model_names` 列表中的每个模型。

**示例：训练、评估、剪枝(global 50%)、蒸馏(MobileNetV2) ResNet50 和 Swin-T**

```bash
python src/main.py \
    --model_names resnet50 swin_t \
    --pretrained \
    --epochs 20 \
    --optimize_mode all \
    --pruning_methods global \
    --pruning_ratios 0.5 \
    --fine_tune_epochs 10 \
    --student_model mobilenetv2 \
    --distill_epochs 30 \
    --output_dir outputs
```

注意：现在不需要指定 `--data_dir` 参数，系统会自动使用项目根目录下的 `old_dataset` 文件夹。

**执行流程:**

1.  创建一个带时间戳的全局运行目录 (例如 `outputs/run_YYYYMMDD_HHMMSS`)。
2.  **对于 `resnet50`**：
    - 在 `outputs/run_.../resnet50/` 下训练模型。
    - 评估训练后的模型。
    - 如果 `optimize_mode` 包含 `prune`，执行剪枝 (结果在 `.../resnet50/pruned/`)。
    - 如果 `optimize_mode` 包含 `distill`，执行蒸馏 (结果在 `.../resnet50/distilled/`)。
    - 如果 `optimize_mode` 包含 `quantize`，打印运行 `quanti.py` 的命令。
3.  **对于 `swin_t`**：
    - 在 `outputs/run_.../swin_t/` 下训练模型。
    - 评估训练后的模型。
    - 如果 `optimize_mode` 包含 `prune`，执行剪枝 (结果在 `.../swin_t/pruned/`)。
    - 如果 `optimize_mode` 包含 `distill`，执行蒸馏 (结果在 `.../swin_t/distilled/`)。
    - 如果 `optimize_mode` 包含 `quantize`，打印运行 `quanti.py` 的命令。
4.  ... 对列表中的其他模型重复此过程。
5.  **最后**：如果 `optimize_mode` 包含 `quantize`，在所有模型处理完毕后，打印用于对整个运行目录进行**批量静态量化**的 `quanti.py` 命令提示。

**关键参数:**

- `--model_names`: 要处理的模型名称列表 (空格分隔，默认: `resnet50 densenet201 vit_s_16 swin_t`)。
- `--pretrained`: 是否使用 ImageNet 预训练权重开始训练。
- `--epochs`: 每个模型的训练轮数。
- `--output_dir`: 保存所有运行结果的基础目录。
- `--optimize_mode`: 选择要执行的优化步骤 ('none', 'prune', 'distill', 'quantize'(仅在最后提示批量命令), 'all'(除量化外都执行))。
- **剪枝参数**: `--pruning_methods`, `--pruning_ratios`, `--fine_tune_epochs`, `--fine_tune_lr`。
- **蒸馏参数**: `--student_model`, `--temperature`, `--alpha`, `--distill_epochs`, `--distill_lr`。

### 静态量化 (独立脚本 `quanti.py`)

`quanti.py` 支持两种模式：

1.  **批量模式 (推荐)**: 在 `main.py` 运行完成后，如果需要对该次运行的所有模型进行量化，根据 `main.py` 输出的提示运行 `quanti.py`，使用 `--run_directory` 指定包含所有模型子目录的全局运行目录。脚本会自动查找每个子目录下的 `model.pth` 进行量化。
2.  **单模型模式**: 对单个模型进行量化，需要指定 `--model_name` 和 `--model_path`。

**示例 (批量模式，基于 `main.py` 的输出提示):**

```bash
python src/quanti.py \
    --run_directory outputs/run_YYYYMMDD_HHMMSS \
    --calibration_batches 10 # 可选，默认50
    # 输出会自动保存在每个模型目录下的 quantized 子目录中
```

**示例 (单模型模式):**

```bash
python src/quanti.py \
    --model_name resnet50 \
    --model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/model.pth \
    --calibration_batches 10 \
    --output_dir outputs/run_YYYYMMDD_HHMMSS/resnet50/quantized # 明确指定输出目录
```

注意：现在量化脚本也会自动使用项目根目录下的 `old_dataset` 文件夹，无需手动指定 `--data_dir` 参数。

在批量模式下，量化结果（模型、评估、大小比较）将自动保存在各自模型目录下的 `quantized` 子目录中 (例如 `outputs/run_.../resnet50/quantized/`, `outputs/run_.../swin_t/quantized/` 等)。在单模型模式下，结果保存在 `--output_dir` 指定的路径。

### 模型比较 (独立脚本 `compare.py`)

使用 `compare.py` 比较两个模型（例如，原始模型和剪枝后的模型）的参数量和稀疏度。

**示例:**

```bash
python src/compare.py \
    --original_model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/model.pth \
    --original_model_name resnet50 \
    --optimized_model_path outputs/run_YYYYMMDD_HHMMSS/resnet50/pruned/global_50/pruned_global_50.pth \
    --optimized_model_name resnet50 \
    --output_file outputs/run_YYYYMMDD_HHMMSS/resnet50/pruned/global_50/compare.txt
```

### Web 演示界面

项目提供了两种用户界面选择：

#### 1. 现代化 Web 界面 (推荐)

基于 FastAPI + HTML/CSS/JS 构建的现代化 Web 界面，提供更好的用户体验。

**启动 Web 界面**

```bash
python src/web_app.py
```

启动后访问: http://localhost:8000

**界面特性**

- 扁平化设计，响应式布局
- 拖拽上传图像文件
- 实时预测结果展示
- 模型性能对比可视化
- 自适应微调控制面板
- RESTful API 接口

#### 2. Gradio 界面 (备选)

基于 Gradio 的传统图形用户界面。

**启动 Gradio 界面**

```bash
python src/app.py
```

启动后，界面将在本地运行，并提供一个可访问的 URL。

**界面功能**

1.  **模型预测**: 上传遥感图像，选择不同的模型进行场景分类预测。

    - 支持 `原始模型(ResNet50)` (如果已训练)
    - 支持 `剪枝模型(Global 50%)` (如果已生成)
    - 支持 `蒸馏模型(MobileNetV2)` (如果已生成)
    - 支持 `量化模型(CPU)` (如果已生成)
    - **新增**: 支持 `DenseNet201` (使用预训练权重)
    - **新增**: 支持 `ViT-S/16` (使用预训练权重)
    - **新增**: 支持 `Swin-T` (使用预训练权重)
      _注意: 首次选择模型进行预测时需要加载模型，可能需要一些时间。_

2.  **模型性能比较**: 界面下方展示了已评估模型的关键性能指标对比。
    - 剪枝模型与原始模型比较 (非零参数量, 准确率等)
    - 蒸馏模型与原始模型比较 (模型大小, 准确率等)
    - 量化模型与原始模型比较 (模型大小, 准确率等)
    - 包含相对百分比，直观展示压缩效果。
      _注: 新添加的 DenseNet201, ViT, Swin 模型由于缺少本项目训练的评估数据，不会出现在比较表中。_

**注意事项**

- 演示界面会自动从`outputs/checkpoints`目录加载模型和评估结果。
- 对于未在本机训练的模型（如 DenseNet201, ViT, Swin），界面将加载 ImageNet 预训练权重进行预测。
- 模型采用懒加载模式，仅在用户选择后才会加载到内存中。
- 量化模型通常只能在 CPU 上运行。
- 上传的图像和 Gradio 临时文件会保存在项目根目录下的 `imgs` 文件夹中。

## 未来工作与改进

- 改进 `optimization/quantization.py` 以更好地支持 ViT/Swin 等 Transformer 模型的静态量化（可能需要第三方库）。
- 考虑为 ViT/Swin 添加动态量化选项。
- 改进 `optimization/pruning.py` 中的 `get_prunable_layers` 以更精确地识别 Transformer 中的可剪枝层。
- 将 `compare.py` 的功能集成到 `main.py` 或 `evaluate.py` 中，自动生成比较文件。
- 添加更多遥感图像特定的数据增强方法。
- 优化 `app.py` 中模型发现和信息加载的逻辑，使其更具鲁棒性，能处理多个运行目录。

## 注意事项

1.  静态量化目前主要对 ResNet 类模型效果较好，ViT/Swin 可能不适用。
2.  剪枝 Transformer 模型可能需要特定于其结构的方法。
3.  知识蒸馏相对通用，可以尝试不同的教师/学生组合。
4.  训练多个模型和优化过程可能需要较长时间和大量计算资源。

## 训练与微调建议

- **强烈建议对 ViT 和 Swin Transformer 等基于 Transformer 的模型使用 ImageNet 预训练权重进行微调 (`--pretrained` 标志，现在默认为 True)。** 在 PatternNet 这样相对较小的数据集上从头训练这些模型通常效果不佳。
- **微调超参数**: 当使用预训练权重进行微调时，建议：
  - 使用较小的学习率，例如 `--learning_rate 1e-4` 或 `1e-5`。
  - 使用较少的训练轮数，例如 `--epochs 20` 到 `50`，具体取决于验证集性能。
  - 可以考虑在 `train.py` 中添加学习率调度器（如带预热的余弦退火）以获得更好的微调效果。
- **从头训练**: 如果确实需要从头训练（不使用 `--pretrained`，即运行时添加 `--no-pretrained`），请确保有足够的数据，并准备进行更长时间的训练和更仔细的超参数调整。

## 自适应微调功能 (新增)

项目新增了自适应微调功能，能够自动监控新数据的分布变化并触发模型微调：

### 功能特性

- **自动监控**：每 10 分钟检测一次`new_dataset`目录下的新数据
- **分布检测**：使用多种方法（均值差异、方差差异、MMD 近似）计算数据分布差异
- **自动微调**：当检测到分布差异超过阈值时，自动触发模型微调
- **知识蒸馏**：微调完成后自动进行知识蒸馏以生成轻量化模型
- **手动控制**：支持手动触发微调和分布检查

### 使用方法

#### 1. 启动 Web 界面

```bash
python src/app.py
```

#### 2. 在"自适应微调控制"标签页中：

- 点击"启动自适应监控"开始监控
- 调整"分布差异阈值"控制触发敏感度（默认 0.1）
- 点击"手动触发微调"立即执行微调
- 点击"检查数据分布"查看当前分布差异

#### 3. 添加新数据

将新的遥感图像放入对应的`new_dataset/类别名/`目录下，系统会自动检测：

```
new_dataset/
├── airplane/        # 新的飞机图像
├── beach/          # 新的海滩图像
├── bridge/         # 新的桥梁图像
└── ...
```

### 技术实现

- **特征提取**：使用训练好的模型提取数据特征
- **分布比较**：计算新旧数据的特征分布差异
- **增量学习**：将新数据合并到训练集进行微调
- **模型替换**：微调后的权重直接替换原模型文件
- **后处理蒸馏**：自动生成对应的蒸馏模型

### 工作流程

1. **监控启动**：启用自适应监控后，系统每 10 分钟检查一次
2. **特征提取**：从`old_dataset`和`new_dataset`中提取特征向量
3. **差异计算**：计算两个数据集的分布差异分数
4. **阈值判断**：如果差异分数超过设定阈值，触发微调
5. **数据合并**：将新数据与旧数据合并成训练集
6. **模型微调**：使用合并数据集对原始模型进行微调
7. **权重替换**：将微调后的权重替换原模型文件
8. **知识蒸馏**：自动执行蒸馏过程生成轻量化模型
9. **数据清理**：清空`new_dataset`等待下一批新数据

### 配置参数

- **分布差异阈值**：0.01-0.5，默认 0.1，可在界面中调整
- **微调轮数**：默认 5 轮，可在代码中修改
- **学习率**：默认 1e-4，适合微调任务
- **蒸馏轮数**：默认 10 轮，自动执行

## 性能优化建议

- 剪枝后进行微调通常能显著恢复精度。
- 蒸馏时调整温度和 alpha 参数可以平衡精度和学生模型性能。
- 使用 Gradio 的懒加载模式可以有效减少演示时的内存占用。
- **新增**：合理设置分布差异阈值可以平衡模型更新频率和计算资源消耗。
