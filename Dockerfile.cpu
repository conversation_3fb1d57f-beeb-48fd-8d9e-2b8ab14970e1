# =============================================================================
# CPU-only Dockerfile for Remote Sensing Image Classification System
# Lightweight image for CPU-only inference and web serving
# =============================================================================

ARG PYTHON_VERSION=3.10
ARG UBUNTU_VERSION=22.04
ARG APP_USER=appuser
ARG APP_UID=1000
ARG APP_GID=1000

# =============================================================================
# Base Image - Ubuntu with Python
# =============================================================================
FROM ubuntu:${UBUNTU_VERSION} as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    LC_ALL=C.UTF-8

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python3-pip \
    python3-venv \
    build-essential \
    curl \
    wget \
    ca-certificates \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create symbolic links for python
RUN ln -sf /usr/bin/python${PYTHON_VERSION} /usr/bin/python3 \
    && ln -sf /usr/bin/python3 /usr/bin/python

# Upgrade pip
RUN python -m pip install --upgrade pip setuptools wheel

# =============================================================================
# Dependencies Stage
# =============================================================================
FROM base as dependencies

# Create application user
RUN groupadd -g ${APP_GID} ${APP_USER} \
    && useradd -u ${APP_UID} -g ${APP_GID} -m -s /bin/bash ${APP_USER}

# Set working directory
WORKDIR /app

# Copy dependency files
COPY requirements.txt pyproject.toml setup.py setup.cfg ./

# Create CPU-specific requirements (without CUDA dependencies)
RUN sed 's/torch>=2.0.0/torch>=2.0.0+cpu/g' requirements.txt > requirements-cpu.txt \
    && sed -i 's/torchvision>=0.15.0/torchvision>=0.15.0+cpu/g' requirements-cpu.txt \
    && sed -i 's/torchaudio>=2.0.0/torchaudio>=2.0.0+cpu/g' requirements-cpu.txt

# Install CPU-optimized PyTorch and dependencies
RUN pip install --no-cache-dir \
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu \
    && pip install --no-cache-dir -r requirements-cpu.txt \
    && pip install --no-cache-dir -e . \
    && pip cache purge

# =============================================================================
# Production Image
# =============================================================================
FROM dependencies as production

# Copy application files
COPY --chown=${APP_USER}:${APP_USER} src/ /app/src/
COPY --chown=${APP_USER}:${APP_USER} static/ /app/static/
COPY --chown=${APP_USER}:${APP_USER} .env.example /app/.env.example

# Create necessary directories
RUN mkdir -p /app/data /app/outputs /app/logs \
    && chown -R ${APP_USER}:${APP_USER} /app \
    && chmod -R 755 /app

# Create health check script
RUN echo '#!/bin/bash\ncurl -f http://localhost:8000/api/health || exit 1' > /app/healthcheck.sh \
    && chmod +x /app/healthcheck.sh \
    && chown ${APP_USER}:${APP_USER} /app/healthcheck.sh

# Switch to non-root user
USER ${APP_USER}

# Set environment variables
ENV ENVIRONMENT=production \
    DEBUG=false \
    RELOAD=false \
    LOG_LEVEL=INFO \
    CUDA_VISIBLE_DEVICES="" \
    OMP_NUM_THREADS=4 \
    MKL_NUM_THREADS=4

# Expose application port
EXPOSE 8000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /app/healthcheck.sh

# Use gunicorn for production serving
CMD ["gunicorn", "src.web_app:app", "-w", "2", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--timeout", "120"]
