#!/usr/bin/env python3
"""
Setup script for Remote Sensing Image Classification System
"""

import os
import sys
from pathlib import Path
from setuptools import setup, find_packages

# Ensure we're in the right directory
here = Path(__file__).parent.absolute()
sys.path.insert(0, str(here / "src"))

# Read version from __init__.py
def get_version():
    """Get version from package __init__.py"""
    version_file = here / "src" / "__init__.py"
    if version_file.exists():
        with open(version_file, "r", encoding="utf-8") as f:
            for line in f:
                if line.startswith("__version__"):
                    return line.split("=")[1].strip().strip('"').strip("'")
    return "0.1.0"

# Read long description from README
def get_long_description():
    """Get long description from README.md"""
    readme_file = here / "README.md"
    if readme_file.exists():
        with open(readme_file, "r", encoding="utf-8") as f:
            return f.read()
    return ""

# Read requirements from requirements.txt
def get_requirements():
    """Get requirements from requirements.txt"""
    requirements_file = here / "requirements.txt"
    requirements = []
    
    if requirements_file.exists():
        with open(requirements_file, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if line and not line.startswith("#"):
                    # Handle version specifiers
                    if ">=" in line:
                        requirements.append(line)
                    else:
                        requirements.append(line)
    
    return requirements

# Read development requirements
def get_dev_requirements():
    """Get development requirements from requirements-dev.txt"""
    dev_requirements_file = here / "requirements-dev.txt"
    requirements = []
    
    if dev_requirements_file.exists():
        with open(dev_requirements_file, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                # Skip comments, empty lines, and -r includes
                if line and not line.startswith("#") and not line.startswith("-r"):
                    requirements.append(line)
    
    return requirements

# Package metadata
PACKAGE_NAME = "remote-sensing-classification"
VERSION = get_version()
DESCRIPTION = "Remote Sensing Image Classification System with Model Optimization"
LONG_DESCRIPTION = get_long_description()
AUTHOR = "Remote Sensing Team"
AUTHOR_EMAIL = "<EMAIL>"
URL = "https://github.com/your-org/remote-sensing-classification"
LICENSE = "MIT"

# Classifiers
CLASSIFIERS = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Recognition",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Operating System :: OS Independent",
]

# Keywords
KEYWORDS = [
    "remote sensing",
    "image classification",
    "deep learning",
    "computer vision",
    "pytorch",
    "machine learning",
    "model optimization",
    "pruning",
    "quantization",
    "knowledge distillation"
]

# Entry points for command line scripts
ENTRY_POINTS = {
    "console_scripts": [
        "rsic-train=src.train:main",
        "rsic-evaluate=src.evaluate:main",
        "rsic-predict=src.app:main",
        "rsic-web=src.web_app:main",
        "rsic-demo=src.demo_web_app:main",
    ],
}

# Package data
PACKAGE_DATA = {
    "": [
        "*.txt",
        "*.md",
        "*.yml",
        "*.yaml",
        "*.json",
        "*.cfg",
        "*.ini",
    ],
}

# Data files
DATA_FILES = [
    ("static", [
        "static/index.html",
        "static/css/style.css",
        "static/js/main.js",
        "static/sw.js",
    ]),
    ("configs", [
        "configs/config.yaml",
    ]) if os.path.exists("configs") else [],
]

# Setup configuration
setup(
    name=PACKAGE_NAME,
    version=VERSION,
    description=DESCRIPTION,
    long_description=LONG_DESCRIPTION,
    long_description_content_type="text/markdown",
    author=AUTHOR,
    author_email=AUTHOR_EMAIL,
    url=URL,
    license=LICENSE,
    
    # Package discovery
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Dependencies
    install_requires=get_requirements(),
    extras_require={
        "dev": get_dev_requirements(),
        "web": [
            "fastapi>=0.100.0",
            "uvicorn[standard]>=0.23.0",
            "python-multipart>=0.0.6",
            "jinja2>=3.1.0",
            "aiofiles>=23.0.0",
        ],
        "ui": [
            "gradio>=3.40.0",
            "streamlit>=1.25.0",
            "plotly>=5.15.0",
        ],
        "monitoring": [
            "wandb>=0.15.0",
            "mlflow>=2.5.0",
            "tensorboard>=2.13.0",
        ],
        "optimization": [
            "pytorch-lightning>=2.0.0",
            "torchmetrics>=1.0.0",
            "albumentations>=1.3.0",
        ],
    },
    
    # Python version requirement
    python_requires=">=3.8",
    
    # Metadata
    classifiers=CLASSIFIERS,
    keywords=" ".join(KEYWORDS),
    
    # Entry points
    entry_points=ENTRY_POINTS,
    
    # Package data
    package_data=PACKAGE_DATA,
    data_files=DATA_FILES,
    include_package_data=True,
    
    # Additional metadata
    project_urls={
        "Bug Reports": f"{URL}/issues",
        "Source": URL,
        "Documentation": f"{URL}/docs",
    },
    
    # Zip safety
    zip_safe=False,
)

# Post-installation message
if __name__ == "__main__":
    print("\n" + "="*60)
    print("Remote Sensing Image Classification System")
    print("="*60)
    print(f"Version: {VERSION}")
    print(f"Installation completed successfully!")
    print("\nQuick start:")
    print("  1. Train a model: rsic-train")
    print("  2. Start web interface: rsic-web")
    print("  3. Run demo: rsic-demo")
    print("\nFor more information, see README.md")
    print("="*60)
