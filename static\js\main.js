/**
 * Enhanced Frontend JavaScript Interaction Logic
 * Remote Sensing Image Classification System
 *
 * Features:
 * - Advanced state management
 * - Performance optimizations
 * - Enhanced error handling
 * - Accessibility improvements
 * - Real-time updates
 * - Offline support
 * - Keyboard shortcuts
 * - Batch operations
 */

// ============================================================================
// GLOBAL STATE MANAGEMENT
// ============================================================================

const AppState = {
    // Core application state
    currentImage: null,
    currentTab: 'prediction',
    monitoringActive: false,
    isOnline: navigator.onLine,

    // UI state
    isLoading: false,
    loadingMessage: '',
    activeModals: new Set(),

    // Data cache
    cache: new Map(),
    models: [],
    predictions: [],

    // Performance monitoring
    performance: {
        apiCalls: 0,
        errors: 0,
        loadTimes: []
    },

    // User preferences
    preferences: {
        autoRefresh: true,
        notifications: true,
        theme: 'light',
        language: 'zh-CN'
    }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

const Utils = {
    // Debounce function for performance optimization
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function for scroll events
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Validate image file
    validateImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!validTypes.includes(file.type)) {
            throw new Error('不支持的文件格式。请选择 JPEG、PNG 或 WebP 格式的图像。');
        }

        if (file.size > maxSize) {
            throw new Error(`文件大小超过限制。最大支持 ${this.formatFileSize(maxSize)}。`);
        }

        return true;
    },

    // Compress image if needed
    async compressImage(file, maxWidth = 1024, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;

                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                canvas.toBlob(resolve, file.type, quality);
            };

            img.src = URL.createObjectURL(file);
        });
    },

    // Copy text to clipboard
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            NotificationManager.show('已复制到剪贴板', 'success');
        } catch (err) {
            console.error('Failed to copy: ', err);
            NotificationManager.show('复制失败', 'error');
        }
    },

    // Download data as file
    downloadAsFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
};

// ============================================================================
// APPLICATION INITIALIZATION
// ============================================================================

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize the application
async function initializeApp() {
    try {
        // Show initial loading
        LoadingManager.show('初始化应用程序...');

        // Initialize core modules
        await Promise.all([
            setupEventListeners(),
            setupKeyboardShortcuts(),
            setupAccessibility(),
            setupPerformanceMonitoring(),
            setupOfflineSupport(),
            loadUserPreferences()
        ]);

        // Initialize UI components
        setupTabNavigation();
        setupImageUpload();
        setupModelSelection();
        setupThresholdSlider();
        setupComparisonTabs();
        setupBatchOperations();
        setupExportFunctionality();

        // Load initial data
        await loadInitialData();

        // Setup real-time updates
        setupRealTimeUpdates();

        NotificationManager.show('应用程序初始化完成', 'success');

    } catch (error) {
        console.error('App initialization failed:', error);
        NotificationManager.show('应用程序初始化失败', 'error');
    } finally {
        LoadingManager.hide();
    }
}

// ============================================================================
// EVENT LISTENERS SETUP
// ============================================================================

async function setupEventListeners() {
    // Online/offline status
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOfflineStatus);

    // Window events
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('resize', Utils.throttle(handleWindowResize, 250));

    // Error handling
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
}

function handleOnlineStatus() {
    AppState.isOnline = true;
    NotificationManager.show('网络连接已恢复', 'success');
    // Retry failed requests
    APIManager.retryFailedRequests();
}

function handleOfflineStatus() {
    AppState.isOnline = false;
    NotificationManager.show('网络连接已断开，部分功能可能不可用', 'warning');
}

function handleBeforeUnload(e) {
    if (AppState.isLoading) {
        e.preventDefault();
        e.returnValue = '有操作正在进行中，确定要离开吗？';
        return e.returnValue;
    }
}

function handleWindowResize() {
    // Adjust layout for responsive design
    adjustLayoutForScreenSize();
}

function handleGlobalError(event) {
    console.error('Global error:', event.error);
    AppState.performance.errors++;
    NotificationManager.show('发生了一个错误，请刷新页面重试', 'error');
}

function handleUnhandledRejection(event) {
    console.error('Unhandled promise rejection:', event.reason);
    AppState.performance.errors++;
}

// ============================================================================
// KEYBOARD SHORTCUTS
// ============================================================================

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + U: Upload image
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
            e.preventDefault();
            document.getElementById('imageInput').click();
        }

        // Escape: Close modals/clear selections
        if (e.key === 'Escape') {
            closeAllModals();
            clearImageSelection();
        }

        // Ctrl/Cmd + Enter: Predict
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (AppState.currentImage && !AppState.isLoading) {
                predictImage();
            }
        }

        // Tab navigation with numbers
        if (e.key >= '1' && e.key <= '4' && e.altKey) {
            e.preventDefault();
            const tabIndex = parseInt(e.key) - 1;
            const tabs = ['prediction', 'comparison', 'dataset', 'adaptive'];
            if (tabs[tabIndex]) {
                switchToTab(tabs[tabIndex]);
            }
        }

        // Ctrl/Cmd + S: Export results
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            exportCurrentResults();
        }
    });
}

// ============================================================================
// ENHANCED TAB NAVIGATION
// ============================================================================

function setupTabNavigation() {
    const navButtons = document.querySelectorAll('.nav-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    navButtons.forEach((button, index) => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            switchToTab(tabId);
        });

        // Add keyboard navigation
        button.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                e.preventDefault();
                const direction = e.key === 'ArrowLeft' ? -1 : 1;
                const nextIndex = (index + direction + navButtons.length) % navButtons.length;
                navButtons[nextIndex].focus();
            }
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                button.click();
            }
        });
    });
}

async function switchToTab(tabId) {
    if (AppState.currentTab === tabId) return;

    const navButtons = document.querySelectorAll('.nav-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // Update active nav button
    navButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-selected', 'false');
    });

    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
        activeButton.setAttribute('aria-selected', 'true');
    }

    // Update active tab content
    tabContents.forEach(content => content.classList.remove('active'));
    const activeContent = document.getElementById(tabId);
    if (activeContent) {
        activeContent.classList.add('active');
    }

    AppState.currentTab = tabId;

    // Load tab-specific data with loading state
    try {
        LoadingManager.show(`加载${getTabDisplayName(tabId)}数据...`);

        if (tabId === 'dataset') {
            await loadDatasetImages();
        } else if (tabId === 'comparison') {
            await loadComparisonData();
        } else if (tabId === 'adaptive') {
            await refreshAdaptiveStatus();
        }
    } catch (error) {
        console.error(`Failed to load ${tabId} data:`, error);
        NotificationManager.show(`加载${getTabDisplayName(tabId)}数据失败`, 'error');
    } finally {
        LoadingManager.hide();
    }

    // Analytics tracking
    trackUserInteraction('tab_switch', { tab: tabId });
}

function getTabDisplayName(tabId) {
    const names = {
        'prediction': '预测',
        'comparison': '对比',
        'dataset': '数据集',
        'adaptive': '自适应微调'
    };
    return names[tabId] || tabId;
}

// ============================================================================
// ENHANCED IMAGE UPLOAD FUNCTIONALITY
// ============================================================================

function setupImageUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    // Enhanced drag and drop with visual feedback
    let dragCounter = 0;

    uploadArea.addEventListener('dragenter', (e) => {
        e.preventDefault();
        dragCounter++;
        uploadArea.classList.add('dragover');
        updateUploadAreaText('释放以上传图像');
    });

    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dragCounter--;
        if (dragCounter === 0) {
            uploadArea.classList.remove('dragover');
            updateUploadAreaText('拖拽图像到此处或点击上传');
        }
    });

    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
    });

    uploadArea.addEventListener('drop', async (e) => {
        e.preventDefault();
        dragCounter = 0;
        uploadArea.classList.remove('dragover');
        updateUploadAreaText('拖拽图像到此处或点击上传');

        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length === 0) {
            NotificationManager.show('请拖拽图像文件', 'warning');
            return;
        }

        if (imageFiles.length > 1) {
            // Handle multiple images
            await handleMultipleImages(imageFiles);
        } else {
            await handleSingleImage(imageFiles[0]);
        }
    });

    // File input change with validation
    imageInput.addEventListener('change', async (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            if (files.length > 1) {
                await handleMultipleImages(files);
            } else {
                await handleSingleImage(files[0]);
            }
        }
    });

    // Paste image from clipboard
    document.addEventListener('paste', async (e) => {
        const items = Array.from(e.clipboardData.items);
        const imageItem = items.find(item => item.type.startsWith('image/'));

        if (imageItem) {
            e.preventDefault();
            const file = imageItem.getAsFile();
            await handleSingleImage(file);
            NotificationManager.show('已从剪贴板粘贴图像', 'success');
        }
    });
}

async function handleSingleImage(file) {
    try {
        // Validate file
        Utils.validateImageFile(file);

        // Show upload progress
        const progressId = showUploadProgress(file.name);

        // Compress image if needed
        let processedFile = file;
        if (file.size > 2 * 1024 * 1024) { // 2MB
            updateUploadProgress(progressId, 30, '压缩图像...');
            processedFile = await Utils.compressImage(file);
        }

        updateUploadProgress(progressId, 60, '处理图像...');

        // Create preview
        const imageUrl = URL.createObjectURL(processedFile);
        await displayImagePreview(imageUrl, file);

        updateUploadProgress(progressId, 100, '完成');

        // Store in state
        AppState.currentImage = processedFile;

        // Update UI
        updatePredictButton();
        hideUploadArea();

        // Analytics
        trackUserInteraction('image_upload', {
            fileSize: file.size,
            fileType: file.type,
            compressed: processedFile !== file
        });

        setTimeout(() => hideUploadProgress(progressId), 1000);

    } catch (error) {
        console.error('Image upload error:', error);
        NotificationManager.show(error.message || '图像上传失败', 'error');
    }
}

async function handleMultipleImages(files) {
    if (files.length > 10) {
        NotificationManager.show('一次最多只能上传10张图像', 'warning');
        return;
    }

    // Show batch upload modal
    showBatchUploadModal(files);
}

async function displayImagePreview(imageUrl, file) {
    return new Promise((resolve, reject) => {
        const uploadArea = document.getElementById('uploadArea');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        previewImg.onload = () => {
            // Add image metadata
            addImageMetadata(file);
            resolve();
        };

        previewImg.onerror = () => {
            reject(new Error('图像加载失败'));
        };

        previewImg.src = imageUrl;
        uploadArea.style.display = 'none';
        imagePreview.style.display = 'block';
    });
}

function addImageMetadata(file) {
    const metadataContainer = document.getElementById('imageMetadata') || createImageMetadataContainer();

    metadataContainer.innerHTML = `
        <div class="metadata-item">
            <span class="metadata-label">文件名:</span>
            <span class="metadata-value">${file.name}</span>
        </div>
        <div class="metadata-item">
            <span class="metadata-label">大小:</span>
            <span class="metadata-value">${Utils.formatFileSize(file.size)}</span>
        </div>
        <div class="metadata-item">
            <span class="metadata-label">类型:</span>
            <span class="metadata-value">${file.type}</span>
        </div>
        <div class="metadata-item">
            <span class="metadata-label">上传时间:</span>
            <span class="metadata-value">${new Date().toLocaleString()}</span>
        </div>
    `;
}

function createImageMetadataContainer() {
    const container = document.createElement('div');
    container.id = 'imageMetadata';
    container.className = 'image-metadata';

    const imagePreview = document.getElementById('imagePreview');
    imagePreview.appendChild(container);

    return container;
}

function updateUploadAreaText(text) {
    const uploadText = document.querySelector('.upload-content h3');
    if (uploadText) {
        uploadText.textContent = text;
    }
}

function hideUploadArea() {
    document.getElementById('uploadArea').style.display = 'none';
}

function showUploadArea() {
    document.getElementById('uploadArea').style.display = 'flex';
    document.getElementById('imagePreview').style.display = 'none';
}

// ============================================================================
// ENHANCED API MANAGEMENT
// ============================================================================

const APIManager = {
    baseURL: '',
    requestQueue: [],
    failedRequests: [],
    cache: new Map(),

    async request(url, options = {}) {
        const requestId = Utils.generateId();
        const startTime = performance.now();

        // Add to performance tracking
        AppState.performance.apiCalls++;

        // Check cache first
        const cacheKey = `${url}_${JSON.stringify(options)}`;
        if (options.method === 'GET' && this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < 300000) { // 5 minutes
                return cached.data;
            }
        }

        // Default options
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: 30000,
            retries: 3,
            ...options
        };

        // Add request to queue
        this.requestQueue.push({ id: requestId, url, options: defaultOptions });

        try {
            const response = await this.fetchWithTimeout(url, defaultOptions);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Cache GET requests
            if (defaultOptions.method === 'GET') {
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
            }

            // Record performance
            const endTime = performance.now();
            AppState.performance.loadTimes.push(endTime - startTime);

            // Remove from queue
            this.requestQueue = this.requestQueue.filter(req => req.id !== requestId);

            return data;

        } catch (error) {
            console.error(`API request failed: ${url}`, error);

            // Add to failed requests for retry
            this.failedRequests.push({
                id: requestId,
                url,
                options: defaultOptions,
                error,
                timestamp: Date.now()
            });

            // Remove from queue
            this.requestQueue = this.requestQueue.filter(req => req.id !== requestId);

            throw error;
        }
    },

    async fetchWithTimeout(url, options) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            throw error;
        }
    },

    async retryFailedRequests() {
        const retryableRequests = this.failedRequests.filter(
            req => Date.now() - req.timestamp < 300000 // 5 minutes
        );

        this.failedRequests = [];

        for (const req of retryableRequests) {
            try {
                await this.request(req.url, req.options);
            } catch (error) {
                console.error('Retry failed:', error);
            }
        }
    },

    clearCache() {
        this.cache.clear();
    }
};

// ============================================================================
// ENHANCED LOADING MANAGEMENT
// ============================================================================

const LoadingManager = {
    activeLoaders: new Set(),

    show(message = '加载中...', id = null) {
        const loaderId = id || Utils.generateId();
        this.activeLoaders.add(loaderId);

        AppState.isLoading = true;
        AppState.loadingMessage = message;

        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');

        if (text) text.textContent = message;
        if (overlay) overlay.style.display = 'flex';

        // Add loading class to body for styling
        document.body.classList.add('loading');

        return loaderId;
    },

    hide(id = null) {
        if (id) {
            this.activeLoaders.delete(id);
        } else {
            this.activeLoaders.clear();
        }

        if (this.activeLoaders.size === 0) {
            AppState.isLoading = false;
            AppState.loadingMessage = '';

            const overlay = document.getElementById('loadingOverlay');
            if (overlay) overlay.style.display = 'none';

            document.body.classList.remove('loading');
        }
    },

    updateMessage(message, id = null) {
        if (this.activeLoaders.size > 0) {
            AppState.loadingMessage = message;
            const text = document.getElementById('loadingText');
            if (text) text.textContent = message;
        }
    }
};

// ============================================================================
// ENHANCED NOTIFICATION MANAGEMENT
// ============================================================================

const NotificationManager = {
    notifications: new Map(),

    show(message, type = 'info', duration = 5000, actions = []) {
        const id = Utils.generateId();
        const notification = this.createNotification(id, message, type, actions);

        const container = document.getElementById('toastContainer');
        if (container) {
            container.appendChild(notification);
        }

        this.notifications.set(id, {
            element: notification,
            type,
            message,
            timestamp: Date.now()
        });

        // Auto-hide after duration
        if (duration > 0) {
            setTimeout(() => this.hide(id), duration);
        }

        // Analytics
        trackUserInteraction('notification_shown', { type, message });

        return id;
    },

    createNotification(id, message, type, actions) {
        const notification = document.createElement('div');
        notification.className = `toast ${type}`;
        notification.setAttribute('data-id', id);
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'polite');

        let actionsHTML = '';
        if (actions.length > 0) {
            actionsHTML = `
                <div class="toast-actions">
                    ${actions.map(action =>
                        `<button class="toast-action" onclick="${action.handler}">${action.label}</button>`
                    ).join('')}
                </div>
            `;
        }

        notification.innerHTML = `
            <div class="toast-content">
                <span class="toast-message">${message}</span>
                <button class="toast-close" onclick="NotificationManager.hide('${id}')" aria-label="关闭">×</button>
            </div>
            ${actionsHTML}
        `;

        return notification;
    },

    hide(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.element.remove();
            this.notifications.delete(id);
        }
    },

    hideAll() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }
};

// ============================================================================
// IMAGE MANAGEMENT FUNCTIONS
// ============================================================================

function clearImage() {
    AppState.currentImage = null;
    showUploadArea();
    document.getElementById('imageInput').value = '';
    document.getElementById('resultsSection').style.display = 'none';

    // Clear metadata
    const metadata = document.getElementById('imageMetadata');
    if (metadata) metadata.remove();

    updatePredictButton();

    // Analytics
    trackUserInteraction('image_cleared');
}

function clearImageSelection() {
    if (AppState.currentImage) {
        clearImage();
        NotificationManager.show('已清除图像选择', 'info');
    }
}

// ============================================================================
// ENHANCED MODEL SELECTION
// ============================================================================

function setupModelSelection() {
    const modelSelect = document.getElementById('modelSelect');

    // Enhanced change handler with debouncing
    modelSelect.addEventListener('change', Utils.debounce(() => {
        updatePredictButton();
        saveUserPreference('lastSelectedModel', modelSelect.value);

        // Show model info if available
        showModelInfo(modelSelect.value);

        // Analytics
        trackUserInteraction('model_selected', { model: modelSelect.value });
    }, 300));

    // Keyboard navigation enhancement
    modelSelect.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && AppState.currentImage && modelSelect.value) {
            e.preventDefault();
            predictImage();
        }
    });

    // Load available models from API
    loadAvailableModels();
}

function showModelInfo(modelKey) {
    if (!modelKey) return;

    const infoContainer = document.getElementById('modelInfo') || createModelInfoContainer();

    // Get model information
    const model = AppState.models.find(m => m.key === modelKey);
    if (model) {
        infoContainer.innerHTML = `
            <div class="model-info-item">
                <span class="info-label">模型类型:</span>
                <span class="info-value">${model.type}</span>
            </div>
            <div class="model-info-item">
                <span class="info-label">优化方式:</span>
                <span class="info-value">${model.variant}</span>
            </div>
            <div class="model-info-item">
                <span class="info-label">预期精度:</span>
                <span class="info-value">${model.accuracy || 'N/A'}</span>
            </div>
        `;
        infoContainer.style.display = 'block';
    } else {
        infoContainer.style.display = 'none';
    }
}

function createModelInfoContainer() {
    const container = document.createElement('div');
    container.id = 'modelInfo';
    container.className = 'model-info';

    const modelSection = document.querySelector('.model-section');
    if (modelSection) {
        modelSection.appendChild(container);
    }

    return container;
}

// Update predict button state with enhanced validation
function updatePredictButton() {
    const predictBtn = document.getElementById('predictBtn');
    const modelSelect = document.getElementById('modelSelect');

    const canPredict = AppState.currentImage &&
                      modelSelect.value &&
                      !AppState.isLoading &&
                      AppState.isOnline;

    predictBtn.disabled = !canPredict;

    // Update button text based on state
    let buttonText = '开始预测';
    if (!AppState.currentImage) {
        buttonText = '请先上传图像';
    } else if (!modelSelect.value) {
        buttonText = '请选择模型';
    } else if (!AppState.isOnline) {
        buttonText = '网络未连接';
    } else if (AppState.isLoading) {
        buttonText = '预测中...';
    }

    const buttonTextElement = predictBtn.querySelector('.btn-text') || predictBtn;
    buttonTextElement.textContent = buttonText;

    // Update accessibility
    predictBtn.setAttribute('aria-disabled', !canPredict);
    if (!canPredict) {
        predictBtn.setAttribute('title', buttonText);
    } else {
        predictBtn.removeAttribute('title');
    }
}

// ============================================================================
// ENHANCED PREDICTION FUNCTIONALITY
// ============================================================================

async function predictImage() {
    if (!AppState.currentImage || !document.getElementById('modelSelect').value) {
        NotificationManager.show('请上传图像并选择模型', 'warning');
        return;
    }

    if (!AppState.isOnline) {
        NotificationManager.show('网络未连接，无法进行预测', 'error');
        return;
    }

    const modelKey = document.getElementById('modelSelect').value;
    const loaderId = LoadingManager.show('正在预测...');

    try {
        // Prepare form data
        const formData = new FormData();
        formData.append('image', AppState.currentImage);
        formData.append('model_key', modelKey);

        // Add prediction to history
        const predictionId = Utils.generateId();
        const prediction = {
            id: predictionId,
            timestamp: Date.now(),
            modelKey,
            fileName: AppState.currentImage.name,
            status: 'pending'
        };

        AppState.predictions.push(prediction);

        // Update loading message
        LoadingManager.updateMessage('发送预测请求...');

        // Make API request
        const response = await fetch('/api/predict', {
            method: 'POST',
            body: formData
        });

        LoadingManager.updateMessage('处理预测结果...');

        const result = await response.json();

        if (response.ok) {
            // Update prediction in history
            const predictionIndex = AppState.predictions.findIndex(p => p.id === predictionId);
            if (predictionIndex !== -1) {
                AppState.predictions[predictionIndex] = {
                    ...prediction,
                    status: 'completed',
                    result,
                    duration: Date.now() - prediction.timestamp
                };
            }

            // Display results
            await displayPredictionResults(result, predictionId);

            // Show success notification
            NotificationManager.show('预测完成', 'success');

            // Analytics
            trackUserInteraction('prediction_completed', {
                modelKey,
                duration: Date.now() - prediction.timestamp,
                topPrediction: Object.keys(result.predictions)[0]
            });

        } else {
            throw new Error(result.detail || '预测失败');
        }

    } catch (error) {
        console.error('Prediction error:', error);

        // Update prediction status
        const predictionIndex = AppState.predictions.findIndex(p => p.id === predictionId);
        if (predictionIndex !== -1) {
            AppState.predictions[predictionIndex].status = 'failed';
            AppState.predictions[predictionIndex].error = error.message;
        }

        // Show error with retry option
        const retryAction = {
            label: '重试',
            handler: 'predictImage()'
        };

        NotificationManager.show(
            error.message || '预测失败，请重试',
            'error',
            10000,
            [retryAction]
        );

        // Analytics
        trackUserInteraction('prediction_failed', {
            modelKey,
            error: error.message
        });

    } finally {
        LoadingManager.hide(loaderId);
        updatePredictButton();
    }
}

// Predict Image
async function predictImage() {
    if (!currentImage || !document.getElementById('modelSelect').value) {
        showToast('请上传图像并选择模型', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('image', currentImage);
    formData.append('model_key', document.getElementById('modelSelect').value);
    
    showLoading('正在预测...');
    
    try {
        const response = await fetch('/api/predict', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            displayPredictionResults(result);
        } else {
            showToast(result.detail || '预测失败', 'error');
        }
    } catch (error) {
        console.error('Prediction error:', error);
        showToast('网络错误，请重试', 'error');
    } finally {
        hideLoading();
    }
}

// ============================================================================
// ENHANCED RESULTS DISPLAY
// ============================================================================

async function displayPredictionResults(result, predictionId = null) {
    const resultsSection = document.getElementById('resultsSection');
    const predictionResults = document.getElementById('predictionResults');
    const inferenceTime = document.getElementById('inferenceTime');

    // Clear previous results with animation
    predictionResults.style.opacity = '0';
    await new Promise(resolve => setTimeout(resolve, 200));
    predictionResults.innerHTML = '';

    // Create enhanced results display
    if (result.predictions) {
        const sortedPredictions = Object.entries(result.predictions)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5); // Top 5 predictions

        sortedPredictions.forEach(([label, confidence], index) => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.style.animationDelay = `${index * 100}ms`;

            // Create confidence bar
            const confidenceBar = document.createElement('div');
            confidenceBar.className = 'confidence-bar';
            confidenceBar.style.width = `${confidence * 100}%`;

            resultItem.innerHTML = `
                <div class="result-content">
                    <span class="result-label" title="${label}">${label}</span>
                    <span class="result-confidence">${(confidence * 100).toFixed(2)}%</span>
                </div>
                <div class="confidence-bar-container">
                    <div class="confidence-bar" style="width: ${confidence * 100}%"></div>
                </div>
            `;

            // Add click handler for more info
            resultItem.addEventListener('click', () => {
                showPredictionDetails(label, confidence, predictionId);
            });

            predictionResults.appendChild(resultItem);
        });
    }

    // Display enhanced inference time
    if (result.inference_time) {
        inferenceTime.innerHTML = `
            <div class="inference-info">
                <div class="inference-time">${result.inference_time}</div>
                <div class="inference-details">
                    <span>模型: ${document.getElementById('modelSelect').selectedOptions[0]?.text}</span>
                    <span>时间: ${new Date().toLocaleTimeString()}</span>
                </div>
            </div>
        `;
    }

    // Add export button
    addExportButton(result, predictionId);

    // Show results with animation
    resultsSection.style.display = 'block';
    predictionResults.style.opacity = '1';

    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function showPredictionDetails(label, confidence, predictionId) {
    const modal = createModal('prediction-details', '预测详情');

    modal.body.innerHTML = `
        <div class="prediction-detail">
            <h3>${label}</h3>
            <div class="detail-item">
                <span class="detail-label">置信度:</span>
                <span class="detail-value">${(confidence * 100).toFixed(4)}%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">预测时间:</span>
                <span class="detail-value">${new Date().toLocaleString()}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">模型:</span>
                <span class="detail-value">${document.getElementById('modelSelect').selectedOptions[0]?.text}</span>
            </div>
            <div class="detail-actions">
                <button class="btn btn-secondary" onclick="Utils.copyToClipboard('${label}')">
                    复制标签
                </button>
                <button class="btn btn-primary" onclick="searchSimilarImages('${label}')">
                    查找相似图像
                </button>
            </div>
        </div>
    `;

    showModal(modal);
}

function addExportButton(result, predictionId) {
    const resultsSection = document.getElementById('resultsSection');

    // Remove existing export button
    const existingBtn = resultsSection.querySelector('.export-results-btn');
    if (existingBtn) existingBtn.remove();

    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn btn-secondary export-results-btn';
    exportBtn.innerHTML = '<i class="fas fa-download"></i> 导出结果';
    exportBtn.onclick = () => exportPredictionResults(result, predictionId);

    resultsSection.appendChild(exportBtn);
}

function exportPredictionResults(result, predictionId) {
    const exportData = {
        predictionId,
        timestamp: new Date().toISOString(),
        model: document.getElementById('modelSelect').value,
        fileName: AppState.currentImage?.name,
        predictions: result.predictions,
        inferenceTime: result.inference_time,
        metadata: {
            userAgent: navigator.userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            language: navigator.language
        }
    };

    const filename = `prediction_${predictionId || Date.now()}.json`;
    Utils.downloadAsFile(JSON.stringify(exportData, null, 2), filename);

    NotificationManager.show('预测结果已导出', 'success');

    // Analytics
    trackUserInteraction('results_exported', { predictionId, format: 'json' });
}

// ============================================================================
// MODAL MANAGEMENT
// ============================================================================

function createModal(id, title) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = id;
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>${title}</h2>
                <button class="modal-close" onclick="closeModal('${id}')">&times;</button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('${id}')">关闭</button>
            </div>
        </div>
    `;

    // Add to DOM
    document.body.appendChild(modal);

    // Add to active modals
    AppState.activeModals.add(id);

    return {
        element: modal,
        body: modal.querySelector('.modal-body'),
        footer: modal.querySelector('.modal-footer')
    };
}

function showModal(modal) {
    modal.element.style.display = 'flex';
    document.body.classList.add('modal-open');

    // Focus management
    const firstFocusable = modal.element.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
        firstFocusable.focus();
    }

    // Escape key handler
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal(modal.element.id);
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);
}

function closeModal(id) {
    const modal = document.getElementById(id);
    if (modal) {
        modal.style.display = 'none';
        modal.remove();
    }

    AppState.activeModals.delete(id);

    if (AppState.activeModals.size === 0) {
        document.body.classList.remove('modal-open');
    }
}

function closeAllModals() {
    AppState.activeModals.forEach(id => closeModal(id));
}

// Threshold Slider Setup
function setupThresholdSlider() {
    const slider = document.getElementById('thresholdSlider');
    const valueDisplay = document.getElementById('thresholdValue');
    
    slider.addEventListener('input', (e) => {
        valueDisplay.textContent = e.target.value;
    });
}

// Comparison Tabs Setup
function setupComparisonTabs() {
    const comparisonTabs = document.querySelectorAll('.comparison-tab-btn');
    
    comparisonTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            comparisonTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            const comparisonType = tab.getAttribute('data-comparison');
            loadComparisonTable(comparisonType);
        });
    });
    
    // Model type selector
    const modelSelect = document.getElementById('comparisonModelSelect');
    modelSelect.addEventListener('change', () => {
        const activeTab = document.querySelector('.comparison-tab-btn.active');
        if (activeTab) {
            const comparisonType = activeTab.getAttribute('data-comparison');
            loadComparisonTable(comparisonType);
        }
    });
}

// Load comparison table
async function loadComparisonTable(comparisonType) {
    const modelType = document.getElementById('comparisonModelSelect').value;
    const container = document.getElementById('comparisonTableContainer');
    
    showLoading('加载对比数据...');
    
    try {
        const response = await fetch(`/api/comparison/${comparisonType}/${modelType}`);
        const result = await response.json();
        
        if (response.ok) {
            container.innerHTML = result.html || '暂无数据';
        } else {
            container.innerHTML = '<p>加载失败</p>';
        }
    } catch (error) {
        console.error('Comparison loading error:', error);
        container.innerHTML = '<p>网络错误</p>';
    } finally {
        hideLoading();
    }
}

// Adaptive Fine-tuning Functions
async function startAdaptiveMonitoring() {
    try {
        const response = await fetch('/api/adaptive/start', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
            monitoringActive = true;
            updateMonitoringStatus(true);
            showToast(result.message, 'success');
            startStatusPolling();
        } else {
            showToast(result.detail || '启动失败', 'error');
        }
    } catch (error) {
        console.error('Start monitoring error:', error);
        showToast('网络错误', 'error');
    }
}

async function stopAdaptiveMonitoring() {
    try {
        const response = await fetch('/api/adaptive/stop', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
            monitoringActive = false;
            updateMonitoringStatus(false);
            showToast(result.message, 'success');
        } else {
            showToast(result.detail || '停止失败', 'error');
        }
    } catch (error) {
        console.error('Stop monitoring error:', error);
        showToast('网络错误', 'error');
    }
}

async function manualFineTune() {
    try {
        const response = await fetch('/api/adaptive/manual-finetune', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
            showToast(result.message, 'success');
            updateFineTuningStatus('微调已启动...');
        } else {
            showToast(result.detail || '启动失败', 'error');
        }
    } catch (error) {
        console.error('Manual fine-tune error:', error);
        showToast('网络错误', 'error');
    }
}

async function checkDataDistribution() {
    showLoading('检查数据分布...');
    
    try {
        const response = await fetch('/api/adaptive/check-distribution');
        const result = await response.json();
        
        if (response.ok) {
            updateDistributionInfo(result);
            showToast('分布检查完成', 'success');
        } else {
            showToast(result.detail || '检查失败', 'error');
        }
    } catch (error) {
        console.error('Distribution check error:', error);
        showToast('网络错误', 'error');
    } finally {
        hideLoading();
    }
}

// Update monitoring status
function updateMonitoringStatus(active) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');
    
    if (active) {
        statusDot.classList.remove('inactive');
        statusDot.classList.add('active');
        statusText.textContent = '监控中';
    } else {
        statusDot.classList.remove('active');
        statusDot.classList.add('inactive');
        statusText.textContent = '未启动';
    }
}

// Update fine-tuning status
function updateFineTuningStatus(message) {
    document.getElementById('fineTuningStatus').textContent = message;
}

// Update distribution info
function updateDistributionInfo(info) {
    const container = document.getElementById('distributionInfo');
    container.innerHTML = `
        <div>差异分数: ${info.difference_score?.toFixed(4) || 'N/A'}</div>
        <div>需要微调: ${info.needs_fine_tuning ? '是' : '否'}</div>
    `;
}

// Start status polling
function startStatusPolling() {
    if (!monitoringActive) return;
    
    setTimeout(async () => {
        try {
            const response = await fetch('/api/adaptive/status');
            const result = await response.json();
            
            if (response.ok) {
                updateFineTuningStatus(result.fine_tuning_status || '等待中...');
            }
        } catch (error) {
            console.error('Status polling error:', error);
        }
        
        if (monitoringActive) {
            startStatusPolling();
        }
    }, 5000); // Poll every 5 seconds
}

// Dataset Functions
async function loadDatasetImages() {
    showLoading('加载数据集图像...');
    
    try {
        const response = await fetch('/api/dataset/samples');
        const result = await response.json();
        
        if (response.ok) {
            displayDatasetImages(result);
        } else {
            showToast('加载数据集失败', 'error');
        }
    } catch (error) {
        console.error('Dataset loading error:', error);
        showToast('网络错误', 'error');
    } finally {
        hideLoading();
    }
}

function displayDatasetImages(data) {
    const oldContainer = document.getElementById('oldDatasetImages');
    const newContainer = document.getElementById('newDatasetImages');
    
    // Display old dataset images
    oldContainer.innerHTML = '';
    if (data.old_images) {
        data.old_images.forEach((imagePath, index) => {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'dataset-image';
            imageDiv.innerHTML = `
                <img src="/api/dataset/image/${encodeURIComponent(imagePath)}" alt="Old dataset sample">
                <div class="dataset-image-label">${data.old_info[index] || ''}</div>
            `;
            oldContainer.appendChild(imageDiv);
        });
    }
    
    // Display new dataset images
    newContainer.innerHTML = '';
    if (data.new_images) {
        data.new_images.forEach((imagePath, index) => {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'dataset-image';
            imageDiv.innerHTML = `
                <img src="/api/dataset/image/${encodeURIComponent(imagePath)}" alt="New dataset sample">
                <div class="dataset-image-label">${data.new_info[index] || ''}</div>
            `;
            newContainer.appendChild(imageDiv);
        });
    }
}

async function refreshDatasetImages() {
    await loadDatasetImages();
    showToast('数据集图像已刷新', 'success');
}

// Utility Functions
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/models');
        const result = await response.json();
        
        if (response.ok && result.models) {
            const select = document.getElementById('modelSelect');
            select.innerHTML = '<option value="">请选择模型...</option>';
            
            result.models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.key;
                option.textContent = model.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Load models error:', error);
    }
}

// ============================================================================
// ENHANCED DATA LOADING FUNCTIONS
// ============================================================================

async function loadInitialData() {
    try {
        // Load available models with caching
        await loadAvailableModels();

        // Load user preferences
        loadUserPreferences();

        // Load dataset images if on dataset tab
        if (AppState.currentTab === 'dataset') {
            await loadDatasetImages();
        }

        // Initialize real-time updates
        setupRealTimeUpdates();

    } catch (error) {
        console.error('Failed to load initial data:', error);
        NotificationManager.show('初始数据加载失败', 'error');
    }
}

async function loadAvailableModels() {
    try {
        const result = await APIManager.request('/api/models');

        if (result && result.models) {
            AppState.models = result.models;

            const select = document.getElementById('modelSelect');
            if (select) {
                select.innerHTML = '<option value="">请选择模型...</option>';

                result.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.key;
                    option.textContent = model.name;
                    option.setAttribute('data-type', model.type);
                    option.setAttribute('data-variant', model.variant);
                    select.appendChild(option);
                });

                // Restore last selected model
                if (AppState.preferences.lastSelectedModel) {
                    select.value = AppState.preferences.lastSelectedModel;
                    showModelInfo(AppState.preferences.lastSelectedModel);
                }
            }

            announceToScreenReader(`已加载 ${result.models.length} 个模型`);
        }

    } catch (error) {
        console.error('Load models error:', error);
        NotificationManager.show('模型列表加载失败', 'error');
    }
}

// ============================================================================
// ENHANCED COMPARISON AND DATASET FUNCTIONS
// ============================================================================

function setupComparisonTabs() {
    const comparisonTabs = document.querySelectorAll('.comparison-tab-btn');

    comparisonTabs.forEach(tab => {
        tab.addEventListener('click', async () => {
            // Update active tab
            comparisonTabs.forEach(t => {
                t.classList.remove('active');
                t.setAttribute('aria-selected', 'false');
            });
            tab.classList.add('active');
            tab.setAttribute('aria-selected', 'true');

            const comparisonType = tab.getAttribute('data-comparison');
            await loadComparisonTable(comparisonType);

            // Analytics
            trackUserInteraction('comparison_tab_selected', { type: comparisonType });
        });

        // Add keyboard navigation
        tab.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                tab.click();
            }
        });
    });

    // Model type selector with debouncing
    const modelSelect = document.getElementById('comparisonModelSelect');
    if (modelSelect) {
        modelSelect.addEventListener('change', Utils.debounce(async () => {
            const activeTab = document.querySelector('.comparison-tab-btn.active');
            if (activeTab) {
                const comparisonType = activeTab.getAttribute('data-comparison');
                await loadComparisonTable(comparisonType);
            }
        }, 500));
    }
}

async function loadComparisonTable(comparisonType) {
    const modelType = document.getElementById('comparisonModelSelect')?.value;
    const container = document.getElementById('comparisonTableContainer');

    if (!modelType || !container) return;

    const loaderId = LoadingManager.show(`加载${comparisonType}对比数据...`);

    try {
        const result = await APIManager.request(`/api/comparison/${comparisonType}/${modelType}`);

        if (result && result.html) {
            container.innerHTML = result.html;

            // Enhance table accessibility
            enhanceTableAccessibility(container);

            announceToScreenReader(`${comparisonType}对比表已更新`);
        } else {
            container.innerHTML = '<p class="no-data">暂无对比数据</p>';
        }

    } catch (error) {
        console.error('Comparison loading error:', error);
        container.innerHTML = '<p class="error-message">加载失败，请重试</p>';
        NotificationManager.show('对比数据加载失败', 'error');
    } finally {
        LoadingManager.hide(loaderId);
    }
}

function enhanceTableAccessibility(container) {
    const tables = container.querySelectorAll('table');
    tables.forEach(table => {
        // Add table caption if missing
        if (!table.querySelector('caption')) {
            const caption = document.createElement('caption');
            caption.textContent = '模型性能对比表';
            table.insertBefore(caption, table.firstChild);
        }

        // Add scope attributes to headers
        const headers = table.querySelectorAll('th');
        headers.forEach(header => {
            if (!header.hasAttribute('scope')) {
                header.setAttribute('scope', 'col');
            }
        });

        // Add table navigation instructions
        table.setAttribute('aria-label', '使用箭头键导航表格');
    });
}

async function loadDatasetImages() {
    const loaderId = LoadingManager.show('加载数据集图像...');

    try {
        const result = await APIManager.request('/api/dataset/samples');

        if (result) {
            displayDatasetImages(result);
            announceToScreenReader('数据集图像已更新');
        }

    } catch (error) {
        console.error('Dataset loading error:', error);
        NotificationManager.show('数据集加载失败', 'error');
    } finally {
        LoadingManager.hide(loaderId);
    }
}

function displayDatasetImages(data) {
    const oldContainer = document.getElementById('oldDatasetImages');
    const newContainer = document.getElementById('newDatasetImages');

    if (!oldContainer || !newContainer) return;

    // Display old dataset images
    oldContainer.innerHTML = '';
    if (data.old_images && data.old_images.length > 0) {
        data.old_images.forEach((imagePath, index) => {
            const imageDiv = createDatasetImageElement(
                imagePath,
                data.old_info?.[index] || '',
                'old-dataset'
            );
            oldContainer.appendChild(imageDiv);
        });
    } else {
        oldContainer.innerHTML = '<p class="no-images">暂无旧数据集图像</p>';
    }

    // Display new dataset images
    newContainer.innerHTML = '';
    if (data.new_images && data.new_images.length > 0) {
        data.new_images.forEach((imagePath, index) => {
            const imageDiv = createDatasetImageElement(
                imagePath,
                data.new_info?.[index] || '',
                'new-dataset'
            );
            newContainer.appendChild(imageDiv);
        });
    } else {
        newContainer.innerHTML = '<p class="no-images">暂无新数据集图像</p>';
    }
}

function createDatasetImageElement(imagePath, info, dataset) {
    const imageDiv = document.createElement('div');
    imageDiv.className = 'dataset-image';
    imageDiv.setAttribute('data-dataset', dataset);

    imageDiv.innerHTML = `
        <img src="/api/dataset/image/${encodeURIComponent(imagePath)}"
             alt="${info || 'Dataset sample'}"
             loading="lazy"
             onerror="this.parentElement.classList.add('image-error')">
        <div class="dataset-image-label">${info || '未知类别'}</div>
        <div class="image-actions">
            <button class="btn-icon" onclick="viewImageFullscreen('${imagePath}')"
                    title="全屏查看" aria-label="全屏查看图像">
                <i class="fas fa-expand"></i>
            </button>
            <button class="btn-icon" onclick="downloadImage('${imagePath}')"
                    title="下载图像" aria-label="下载图像">
                <i class="fas fa-download"></i>
            </button>
        </div>
    `;

    return imageDiv;
}

async function refreshDatasetImages() {
    await loadDatasetImages();
    NotificationManager.show('数据集图像已刷新', 'success');
    trackUserInteraction('dataset_refreshed');
}

// ============================================================================
// REAL-TIME UPDATES
// ============================================================================

function setupRealTimeUpdates() {
    // Setup periodic status updates
    if (AppState.preferences.autoRefresh) {
        setInterval(async () => {
            if (AppState.currentTab === 'adaptive' && AppState.monitoringActive) {
                await refreshAdaptiveStatus();
            }
        }, 10000); // Every 10 seconds
    }

    // Setup WebSocket connection if available
    setupWebSocketConnection();
}

function setupWebSocketConnection() {
    // WebSocket implementation for real-time updates
    // This would connect to a WebSocket server for live updates
    try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        const ws = new WebSocket(wsUrl);

        ws.onopen = () => {
            console.log('WebSocket connected');
            NotificationManager.show('实时更新已连接', 'success', 3000);
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('WebSocket message parsing error:', error);
            }
        };

        ws.onclose = () => {
            console.log('WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(setupWebSocketConnection, 5000);
        };

        ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };

    } catch (error) {
        console.log('WebSocket not available:', error);
    }
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'status_update':
            updateAdaptiveStatus(data.payload);
            break;
        case 'new_prediction':
            handleNewPredictionNotification(data.payload);
            break;
        case 'system_alert':
            NotificationManager.show(data.payload.message, data.payload.type);
            break;
        default:
            console.log('Unknown WebSocket message type:', data.type);
    }
}

// ============================================================================
// EXPORT FUNCTIONALITY
// ============================================================================

function setupExportFunctionality() {
    // Add export buttons to various sections
    addExportButtons();
}

function addExportButtons() {
    // Add export button to comparison section
    const comparisonContent = document.querySelector('.comparison-content');
    if (comparisonContent) {
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-secondary export-btn';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> 导出对比数据';
        exportBtn.onclick = exportComparisonData;
        comparisonContent.appendChild(exportBtn);
    }

    // Add export button to dataset section
    const datasetContainer = document.querySelector('.dataset-container');
    if (datasetContainer) {
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-secondary export-btn';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> 导出数据集信息';
        exportBtn.onclick = exportDatasetInfo;
        datasetContainer.parentNode.appendChild(exportBtn);
    }
}

function exportCurrentResults() {
    if (AppState.predictions.length === 0) {
        NotificationManager.show('暂无预测结果可导出', 'warning');
        return;
    }

    const latestPrediction = AppState.predictions[AppState.predictions.length - 1];
    if (latestPrediction.result) {
        exportPredictionResults(latestPrediction.result, latestPrediction.id);
    }
}

function exportComparisonData() {
    const tables = document.querySelectorAll('.comparison-content table');
    if (tables.length === 0) {
        NotificationManager.show('暂无对比数据可导出', 'warning');
        return;
    }

    const data = Array.from(tables).map(table => {
        const rows = Array.from(table.rows);
        return rows.map(row =>
            Array.from(row.cells).map(cell => cell.textContent.trim())
        );
    });

    const exportData = {
        type: 'comparison',
        timestamp: new Date().toISOString(),
        data
    };

    Utils.downloadAsFile(
        JSON.stringify(exportData, null, 2),
        `comparison_${Date.now()}.json`
    );

    NotificationManager.show('对比数据已导出', 'success');
    trackUserInteraction('comparison_exported');
}

function exportDatasetInfo() {
    const oldImages = document.querySelectorAll('#oldDatasetImages .dataset-image');
    const newImages = document.querySelectorAll('#newDatasetImages .dataset-image');

    const exportData = {
        type: 'dataset_info',
        timestamp: new Date().toISOString(),
        oldDataset: Array.from(oldImages).map(img => ({
            src: img.querySelector('img')?.src,
            label: img.querySelector('.dataset-image-label')?.textContent
        })),
        newDataset: Array.from(newImages).map(img => ({
            src: img.querySelector('img')?.src,
            label: img.querySelector('.dataset-image-label')?.textContent
        }))
    };

    Utils.downloadAsFile(
        JSON.stringify(exportData, null, 2),
        `dataset_info_${Date.now()}.json`
    );

    NotificationManager.show('数据集信息已导出', 'success');
    trackUserInteraction('dataset_exported');
}

// ============================================================================
// ENHANCED ADAPTIVE FUNCTIONS (PLACEHOLDER)
// ============================================================================

async function refreshAdaptiveStatus() {
    // This would be implemented with the actual adaptive monitoring functions
    // For now, just update the UI state
    if (AppState.monitoringActive) {
        updateMonitoringStatus(true);
    }
}

function updateAdaptiveStatus(data) {
    // Update adaptive monitoring status from WebSocket or API
    if (data.monitoring !== undefined) {
        AppState.monitoringActive = data.monitoring;
        updateMonitoringStatus(data.monitoring);
    }

    if (data.fineTuningStatus) {
        updateFineTuningStatus(data.fineTuningStatus);
    }

    if (data.distributionInfo) {
        updateDistributionInfo(data.distributionInfo);
    }
}

// Placeholder functions for adaptive monitoring (to be implemented)
function setupThresholdSlider() {
    const slider = document.getElementById('thresholdSlider');
    const valueDisplay = document.getElementById('thresholdValue');

    if (slider && valueDisplay) {
        slider.addEventListener('input', Utils.debounce((e) => {
            valueDisplay.textContent = e.target.value;
            saveUserPreference('adaptiveThreshold', e.target.value);
            trackUserInteraction('threshold_changed', { value: e.target.value });
        }, 300));
    }
}

function updateMonitoringStatus(active) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-text');

    if (statusDot && statusText) {
        if (active) {
            statusDot.classList.remove('inactive');
            statusDot.classList.add('active');
            statusText.textContent = '监控中';
        } else {
            statusDot.classList.remove('active');
            statusDot.classList.add('inactive');
            statusText.textContent = '未启动';
        }
    }
}

function updateFineTuningStatus(message) {
    const statusElement = document.getElementById('fineTuningStatus');
    if (statusElement) {
        statusElement.textContent = message;
    }
}

function updateDistributionInfo(info) {
    const container = document.getElementById('distributionInfo');
    if (container) {
        container.innerHTML = `
            <div>差异分数: ${info.difference_score?.toFixed(4) || 'N/A'}</div>
            <div>需要微调: ${info.needs_fine_tuning ? '是' : '否'}</div>
        `;
    }
}

// ============================================================================
// ADDITIONAL UTILITY FUNCTIONS
// ============================================================================

function viewImageFullscreen(imagePath) {
    const modal = createModal('image-fullscreen', '图像查看');
    modal.body.innerHTML = `
        <div class="fullscreen-image-container">
            <img src="/api/dataset/image/${encodeURIComponent(imagePath)}"
                 alt="Full screen image"
                 style="max-width: 100%; max-height: 80vh; object-fit: contain;">
        </div>
    `;
    showModal(modal);
}

function downloadImage(imagePath) {
    const link = document.createElement('a');
    link.href = `/api/dataset/image/${encodeURIComponent(imagePath)}`;
    link.download = imagePath.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    NotificationManager.show('图像下载已开始', 'success');
    trackUserInteraction('image_downloaded', { path: imagePath });
}

function searchSimilarImages(label) {
    // Placeholder for similar image search functionality
    NotificationManager.show(`搜索与"${label}"相似的图像功能正在开发中`, 'info');
    trackUserInteraction('similar_search_requested', { label });
}

// ============================================================================
// INITIALIZATION COMPLETION
// ============================================================================

// Ensure all functions are properly loaded
console.log('Enhanced JavaScript interaction logic loaded successfully');

// Export functions for global access if needed
window.AppState = AppState;
window.Utils = Utils;
window.APIManager = APIManager;
window.LoadingManager = LoadingManager;
window.NotificationManager = NotificationManager;

async function loadComparisonData() {
    // Load initial comparison data
    const activeTab = document.querySelector('.comparison-tab-btn.active');
    if (activeTab) {
        const comparisonType = activeTab.getAttribute('data-comparison');
        await loadComparisonTable(comparisonType);
    }
}

// ============================================================================
// BATCH OPERATIONS
// ============================================================================

function setupBatchOperations() {
    // Add batch upload button
    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea) {
        const batchBtn = document.createElement('button');
        batchBtn.className = 'btn btn-secondary batch-upload-btn';
        batchBtn.innerHTML = '<i class="fas fa-images"></i> 批量上传';
        batchBtn.onclick = () => {
            const input = document.getElementById('imageInput');
            input.multiple = true;
            input.click();
        };

        uploadArea.appendChild(batchBtn);
    }
}

function showBatchUploadModal(files) {
    const modal = createModal('batch-upload', '批量图像预测');

    modal.body.innerHTML = `
        <div class="batch-upload-container">
            <div class="batch-info">
                <p>已选择 ${files.length} 张图像进行批量预测</p>
                <div class="batch-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="progress-text">0 / ${files.length}</span>
                </div>
            </div>
            <div class="batch-files">
                ${files.map((file, index) => `
                    <div class="batch-file-item" data-index="${index}">
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">${Utils.formatFileSize(file.size)}</span>
                        <span class="file-status">等待中</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    modal.footer.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal('batch-upload')">取消</button>
        <button class="btn btn-primary" onclick="startBatchPrediction()">开始批量预测</button>
    `;

    // Store files in modal data
    modal.element.dataset.files = JSON.stringify(files.map(f => f.name));

    showModal(modal);
}

async function startBatchPrediction() {
    const modal = document.getElementById('batch-upload');
    const files = JSON.parse(modal.dataset.files);
    const modelKey = document.getElementById('modelSelect').value;

    if (!modelKey) {
        NotificationManager.show('请先选择预测模型', 'warning');
        return;
    }

    const results = [];
    let completed = 0;

    for (let i = 0; i < files.length; i++) {
        const fileItem = modal.querySelector(`[data-index="${i}"]`);
        const statusElement = fileItem.querySelector('.file-status');

        try {
            statusElement.textContent = '预测中...';
            statusElement.className = 'file-status processing';

            // Simulate prediction (replace with actual API call)
            await new Promise(resolve => setTimeout(resolve, 1000));

            statusElement.textContent = '完成';
            statusElement.className = 'file-status completed';

            completed++;
            updateBatchProgress(completed, files.length);

        } catch (error) {
            statusElement.textContent = '失败';
            statusElement.className = 'file-status failed';
        }
    }

    NotificationManager.show(`批量预测完成，成功处理 ${completed}/${files.length} 张图像`, 'success');
}

function updateBatchProgress(completed, total) {
    const modal = document.getElementById('batch-upload');
    const progressFill = modal.querySelector('.progress-fill');
    const progressText = modal.querySelector('.progress-text');

    const percentage = (completed / total) * 100;
    progressFill.style.width = `${percentage}%`;
    progressText.textContent = `${completed} / ${total}`;
}

// ============================================================================
// ACCESSIBILITY ENHANCEMENTS
// ============================================================================

function setupAccessibility() {
    // Add ARIA labels and roles
    addAriaLabels();

    // Setup focus management
    setupFocusManagement();

    // Add screen reader announcements
    setupScreenReaderSupport();

    // High contrast mode detection
    setupHighContrastMode();
}

function addAriaLabels() {
    // Tab navigation
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach((btn, index) => {
        btn.setAttribute('role', 'tab');
        btn.setAttribute('aria-selected', btn.classList.contains('active'));
        btn.setAttribute('aria-controls', btn.getAttribute('data-tab'));
        btn.setAttribute('tabindex', btn.classList.contains('active') ? '0' : '-1');
    });

    // Tab panels
    const tabPanels = document.querySelectorAll('.tab-content');
    tabPanels.forEach(panel => {
        panel.setAttribute('role', 'tabpanel');
        panel.setAttribute('aria-hidden', !panel.classList.contains('active'));
    });

    // Form elements
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        const label = select.previousElementSibling;
        if (label && label.tagName === 'LABEL') {
            const id = select.id || Utils.generateId();
            select.id = id;
            label.setAttribute('for', id);
        }
    });
}

function setupFocusManagement() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main';
    skipLink.className = 'skip-link';
    skipLink.textContent = '跳转到主要内容';
    document.body.insertBefore(skipLink, document.body.firstChild);

    // Focus trap for modals
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab' && AppState.activeModals.size > 0) {
            trapFocusInModal(e);
        }
    });
}

function trapFocusInModal(e) {
    const activeModal = document.querySelector('.modal:last-of-type');
    if (!activeModal) return;

    const focusableElements = activeModal.querySelectorAll(
        'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
    } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
    }
}

function setupScreenReaderSupport() {
    // Create live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.id = 'live-region';
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';

    document.body.appendChild(liveRegion);
}

function announceToScreenReader(message) {
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
        liveRegion.textContent = message;
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }
}

function setupHighContrastMode() {
    // Detect high contrast mode
    if (window.matchMedia('(prefers-contrast: high)').matches) {
        document.body.classList.add('high-contrast');
    }

    // Listen for changes
    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
        if (e.matches) {
            document.body.classList.add('high-contrast');
        } else {
            document.body.classList.remove('high-contrast');
        }
    });
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

function setupPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
        const perfData = performance.getEntriesByType('navigation')[0];
        AppState.performance.pageLoadTime = perfData.loadEventEnd - perfData.loadEventStart;

        console.log('Page load performance:', {
            loadTime: AppState.performance.pageLoadTime,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            firstPaint: performance.getEntriesByType('paint')[0]?.startTime
        });
    });

    // Monitor memory usage
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            AppState.performance.memoryUsage = {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit
            };
        }, 30000); // Every 30 seconds
    }

    // Monitor API performance
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
        const start = performance.now();
        try {
            const response = await originalFetch(...args);
            const end = performance.now();
            AppState.performance.apiTimes = AppState.performance.apiTimes || [];
            AppState.performance.apiTimes.push(end - start);
            return response;
        } catch (error) {
            const end = performance.now();
            AppState.performance.apiErrors = AppState.performance.apiErrors || [];
            AppState.performance.apiErrors.push({
                url: args[0],
                duration: end - start,
                error: error.message
            });
            throw error;
        }
    };
}

// ============================================================================
// ANALYTICS AND TRACKING
// ============================================================================

function trackUserInteraction(action, data = {}) {
    const event = {
        action,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...data
    };

    // Store locally for now (could send to analytics service)
    const interactions = JSON.parse(localStorage.getItem('userInteractions') || '[]');
    interactions.push(event);

    // Keep only last 1000 interactions
    if (interactions.length > 1000) {
        interactions.splice(0, interactions.length - 1000);
    }

    localStorage.setItem('userInteractions', JSON.stringify(interactions));

    console.log('User interaction tracked:', event);
}

// ============================================================================
// OFFLINE SUPPORT
// ============================================================================

function setupOfflineSupport() {
    // Register service worker if available
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }

    // Cache critical resources
    cacheResources();
}

function cacheResources() {
    // Cache essential files for offline use
    const resourcesToCache = [
        '/',
        '/static/css/style.css',
        '/static/js/main.js',
        '/api/models'
    ];

    if ('caches' in window) {
        caches.open('app-cache-v1').then(cache => {
            cache.addAll(resourcesToCache);
        });
    }
}

// ============================================================================
// USER PREFERENCES
// ============================================================================

function loadUserPreferences() {
    const saved = localStorage.getItem('userPreferences');
    if (saved) {
        try {
            const preferences = JSON.parse(saved);
            AppState.preferences = { ...AppState.preferences, ...preferences };
            applyUserPreferences();
        } catch (error) {
            console.error('Failed to load user preferences:', error);
        }
    }
}

function saveUserPreference(key, value) {
    AppState.preferences[key] = value;
    localStorage.setItem('userPreferences', JSON.stringify(AppState.preferences));
}

function applyUserPreferences() {
    // Apply theme
    if (AppState.preferences.theme) {
        document.body.className = document.body.className.replace(/theme-\w+/, '');
        document.body.classList.add(`theme-${AppState.preferences.theme}`);
    }

    // Apply last selected model
    if (AppState.preferences.lastSelectedModel) {
        const modelSelect = document.getElementById('modelSelect');
        if (modelSelect) {
            modelSelect.value = AppState.preferences.lastSelectedModel;
        }
    }
}

// ============================================================================
// UTILITY FUNCTIONS FOR UPLOAD PROGRESS
// ============================================================================

function showUploadProgress(fileName) {
    const progressId = Utils.generateId();
    const progressContainer = document.getElementById('uploadProgressContainer') || createUploadProgressContainer();

    const progressItem = document.createElement('div');
    progressItem.className = 'upload-progress-item';
    progressItem.id = `progress-${progressId}`;
    progressItem.innerHTML = `
        <div class="progress-info">
            <span class="progress-filename">${fileName}</span>
            <span class="progress-percentage">0%</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
        <div class="progress-status">准备中...</div>
    `;

    progressContainer.appendChild(progressItem);
    progressContainer.style.display = 'block';

    return progressId;
}

function updateUploadProgress(progressId, percentage, status) {
    const progressItem = document.getElementById(`progress-${progressId}`);
    if (progressItem) {
        const fill = progressItem.querySelector('.progress-fill');
        const percentageSpan = progressItem.querySelector('.progress-percentage');
        const statusSpan = progressItem.querySelector('.progress-status');

        fill.style.width = `${percentage}%`;
        percentageSpan.textContent = `${percentage}%`;
        statusSpan.textContent = status;
    }
}

function hideUploadProgress(progressId) {
    const progressItem = document.getElementById(`progress-${progressId}`);
    if (progressItem) {
        progressItem.remove();

        const container = document.getElementById('uploadProgressContainer');
        if (container && container.children.length === 0) {
            container.style.display = 'none';
        }
    }
}

function createUploadProgressContainer() {
    const container = document.createElement('div');
    container.id = 'uploadProgressContainer';
    container.className = 'upload-progress-container';
    container.style.display = 'none';

    const uploadArea = document.getElementById('uploadArea');
    if (uploadArea && uploadArea.parentNode) {
        uploadArea.parentNode.insertBefore(container, uploadArea.nextSibling);
    }

    return container;
}

// ============================================================================
// LAYOUT ADJUSTMENT FUNCTIONS
// ============================================================================

function adjustLayoutForScreenSize() {
    const width = window.innerWidth;

    if (width < 768) {
        document.body.classList.add('mobile-layout');
        document.body.classList.remove('desktop-layout');
    } else {
        document.body.classList.add('desktop-layout');
        document.body.classList.remove('mobile-layout');
    }

    // Adjust grid layouts
    const grids = document.querySelectorAll('.prediction-container, .dataset-container, .adaptive-container');
    grids.forEach(grid => {
        if (width < 768) {
            grid.style.gridTemplateColumns = '1fr';
        } else {
            grid.style.gridTemplateColumns = '';
        }
    });
}

// ============================================================================
// LEGACY COMPATIBILITY FUNCTIONS
// ============================================================================

// Maintain compatibility with existing code
function showLoading(text = '加载中...') {
    return LoadingManager.show(text);
}

function hideLoading() {
    LoadingManager.hide();
}

function showToast(message, type = 'info') {
    return NotificationManager.show(message, type);
}
