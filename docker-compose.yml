version: '3.8'

services:
  # Main application service
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - PYTHON_VERSION=3.10
    container_name: rsic-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - HOST=0.0.0.0
      - PORT=8000
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DATABASE=remote_sensing_db
      - REDIS_URL=redis://redis:6379
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    volumes:
      - ./data:/app/data
      - ./outputs:/app/outputs
      - ./logs:/app/logs
      - ./static:/app/static
    depends_on:
      - mongodb
      - redis
    networks:
      - rsic-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB database
  mongodb:
    image: mongo:6.0
    container_name: rsic-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=remote_sensing_db
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - rsic-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: rsic-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - rsic-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MLflow tracking server
  mlflow:
    image: python:3.10-slim
    container_name: rsic-mlflow
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=sqlite:///mlflow/mlflow.db
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=/mlflow/artifacts
    volumes:
      - mlflow_data:/mlflow
    networks:
      - rsic-network
    restart: unless-stopped
    command: >
      bash -c "
        pip install mlflow[extras] &&
        mlflow server 
        --backend-store-uri sqlite:///mlflow/mlflow.db 
        --default-artifact-root /mlflow/artifacts 
        --host 0.0.0.0 
        --port 5000
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jupyter notebook (development)
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile.jupyter
    container_name: rsic-jupyter
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=your-secure-token
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./src:/app/src
      - ./outputs:/app/outputs
    networks:
      - rsic-network
    restart: unless-stopped
    profiles:
      - development

  # TensorBoard (monitoring)
  tensorboard:
    image: tensorflow/tensorflow:latest
    container_name: rsic-tensorboard
    ports:
      - "6006:6006"
    volumes:
      - ./logs/tensorboard:/logs
    networks:
      - rsic-network
    restart: unless-stopped
    command: tensorboard --logdir=/logs --host=0.0.0.0 --port=6006
    profiles:
      - monitoring

  # Nginx reverse proxy (production)
  nginx:
    image: nginx:alpine
    container_name: rsic-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - app
    networks:
      - rsic-network
    restart: unless-stopped
    profiles:
      - production

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: rsic-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - rsic-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: rsic-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - rsic-network
    restart: unless-stopped
    profiles:
      - monitoring

# Named volumes for data persistence
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  mlflow_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Custom network
networks:
  rsic-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
# docker-compose.dev.yml (development overrides)
version: '3.8'

services:
  app:
    build:
      target: development
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - RELOAD=true
      - LOG_LEVEL=DEBUG
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/.pytest_cache
    ports:
      - "8000:8000"
      - "8001:8001"  # Additional port for debugging
    command: >
      bash -c "
        pip install -e .[dev] &&
        uvicorn src.web_app:app --host 0.0.0.0 --port 8000 --reload
      "

  # Development database with test data
  mongodb:
    environment:
      - MONGO_INITDB_ROOT_USERNAME=dev
      - MONGO_INITDB_ROOT_PASSWORD=dev123
    volumes:
      - ./scripts/dev-data.js:/docker-entrypoint-initdb.d/dev-data.js:ro

# Production override
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
---
# docker-compose.prod.yml (production overrides)
version: '3.8'

services:
  app:
    build:
      target: production
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - RELOAD=false
      - LOG_LEVEL=INFO
      - WORKERS=4
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    restart: always

  mongodb:
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USER}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
